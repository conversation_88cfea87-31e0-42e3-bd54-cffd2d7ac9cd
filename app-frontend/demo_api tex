//Table: usermanagement_user
//Method: POST
//URL: http://8000/api/v1/userapi/forgot-password/
{
  "email": "<EMAIL>"
}
Example Response JSON (Success):
{
  "status": "success",
  "message": "OTP sent to your email."
}
Example Response JSON (Error):
{
  "status": "error",
  "message": "Email not found."
}


2. Reset Password API
Table: usermanagement_user
Method: POST
URL: http://8000/api/v1/userapi/reset-password/

Request JSON:
{
  "email": "<EMAIL>",
  "otp": "string",
  "password": "string"
}

Example Response JSON (Success):
{
  "status": "success",
  "message": "Password has been reset successfully."
}

Example Response JSON (Error):
{
  "status": "error",
  "message": "Invalid OTP or expired."
}





3. Business Operator Devices API
Table: lips_business_operator_masters
Method: GET
URL: http://8000/api/v1/business-operator-masters/device/

Request JSON:
{
  "domain_name": "**************",        // from lips_business_operator_masters
  "display_device_id": "67890"            // from lips_device_masters
}


Example Response JSON (Success):
{
  "status": "success",
  "data": {
    "business_operator_id": 123,
    "operator_name": "ABC Business Operator",
    "domain_name": "**************",
    "devices": [
      {
        "device_id": 67890,
        "device_name": "Device A",
        "status": "active"
      }
    ]
  }
}

Example Response JSON (Error):
{
  "status": "error",
  "message": "Business operator or device not found."
}

4. Work Info API
Table: lips_work_info
Method: GET
URL: http://8000/api/v1/lips-work-info/work/

Request JSON:
{
  "work_name": "SVLR0008",              // from lips_work_info
  "display_device_id": "67890",         // from lips_device_masters
  "status": "areaChange",                // from lips_device_masters
  "battery": "60%"                      // from lips_device_masters
}

Example Response JSON (Success):
{
  "status": "success",
  "data": {
    "work_name": "SVLR0008",
    "device": {
      "display_device_id": "67890",
      "status": "areaChange",
      "battery": "60%"
    },
    "work_description": "Site visit at location XYZ",
    "start_time": "2025-05-23T09:00:00Z",
    "end_time": "2025-05-23T17:00:00Z"
  }
}


Example Response JSON (Error):
{
  "status": "error",
  "message": "Work info not found."
}
