# lips_app

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

### Prerequisites

Ensure you have the following installed on your system:

- [Flutter SDK](https://docs.flutter.dev/get-started/install) (version 3.29.2 or later)
- [Dart SDK](https://dart.dev/get-dart) (comes with Flutter)
- Android Studio or Visual Studio Code (with Flutter and Dart plugins)
- Git (for version control)
- A device or emulator for testing (Android/iOS)

### Setup Instructions

1. **Clone the Repository**  
   Open a terminal and run:

   ```bash
   git clone https://itage-dev-private.com/ts/corporate-planning-office/lips.git
   cd Lips/lips_app
   ```

2. **Install Flutter Dependencies**  
   Run the following command to fetch all required dependencies:

   ```bash
   flutter pub get
   ```

3. **Set Up an Emulator or Device**

   - For Android: Use Android Studio to create and start an emulator.
   - For iOS: Use Xcode to create and start a simulator.
   - Alternatively, connect a physical device via USB and enable developer mode.

4. **Run the Application**  
   Use the following command to run the app:

   ```bash
   flutter run
   ```

5. **Build the Application (Optional)**  
   To build the app for release:
   - For Android:
     ```bash
     flutter build apk
     ```
   - For iOS:
     ```bash
     flutter build ios
     ```

### Additional Resources

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
