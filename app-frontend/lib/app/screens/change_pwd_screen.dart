import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/change_password_controller.dart';
import '../routes/app_routes.dart';
import '../utils/app_colors.dart';
import '../utils/widgets/custom_primary_button.dart';

class ChangePwdScreen extends StatefulWidget {
  const ChangePwdScreen({super.key});

  @override
  _ChangePwdScreenState createState() => _ChangePwdScreenState();
}

class _ChangePwdScreenState extends State<ChangePwdScreen> {
  bool _obscureTextCurrent = true;
  bool _obscureTextNew = true;
  bool _obscureTextConfirm = true;

  final TextEditingController _currentPwdController = TextEditingController();
  final TextEditingController _newPwdController = TextEditingController();
  final TextEditingController _confirmPwdController = TextEditingController();

  final ChangePasswordController _controller = Get.put(ChangePasswordController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const SizedBox.shrink(),
        title: const Text('パスワード変更画面'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),

            // Current Password
            TextField(
              controller: _currentPwdController,
              obscureText: _obscureTextCurrent,
              decoration: InputDecoration(
                labelText: '現在のパスワード',
                border: const OutlineInputBorder(),
                hintText: '現在のパスワードを入力してください',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureTextCurrent ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.blueColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureTextCurrent = !_obscureTextCurrent;
                    });
                  },
                ),
              ),
            ),
            SizedBox(height: 20.h),

            // New Password
            TextField(
              controller: _newPwdController,
              obscureText: _obscureTextNew,
              decoration: InputDecoration(
                labelText: '新しいパスワード',
                border: const OutlineInputBorder(),
                hintText: '新しいパスワードを入力してください',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureTextNew ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.blueColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureTextNew = !_obscureTextNew;
                    });
                  },
                ),
              ),
            ),
            SizedBox(height: 20.h),

            // Confirm New Password
            TextField(
              controller: _confirmPwdController,
              obscureText: _obscureTextConfirm,
              decoration: InputDecoration(
                labelText: '新しいパスワード（再確認）',
                border: const OutlineInputBorder(),
                hintText: '新しいパスワードを再確認してください',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureTextConfirm ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.blueColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureTextConfirm = !_obscureTextConfirm;
                    });
                  },
                ),
              ),
            ),
            SizedBox(height: 40.h),

            Obx(() {
              if (_controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              return CustomButtonPrimary(
                onPressed: () async {
                  // Validate the inputs first
                  String currentPassword = _currentPwdController.text.trim();
                  String newPassword = _newPwdController.text.trim();
                  String confirmPassword = _confirmPwdController.text.trim();

                  // Frontend validation
                  if (currentPassword.isEmpty || newPassword.isEmpty || confirmPassword.isEmpty) {
                    Get.snackbar(
                      'エラー',
                      'すべての項目を入力してください', // "Please fill in all fields"
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.redAccent,
                      colorText: Colors.white,
                    );
                    return;
                  }

                  if (newPassword != confirmPassword) {
                    Get.snackbar(
                      'パスワードエラー',
                      '新しいパスワードが一致しません', // "Passwords do not match"
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.redAccent,
                      colorText: Colors.white,
                    );
                    return;
                  }

                  // ✅ Call the ChangePasswordController to change the password
                  await _controller.changePassword(
                    currentPassword: currentPassword,
                    newPassword: newPassword,
                  );

                  if (_controller.message.value.isNotEmpty) {
                    Get.snackbar(
                      "パスワード変更",
                      _controller.message.value,
                      snackPosition: SnackPosition.BOTTOM,
                    );

                    if (_controller.message.value == "Password changed successfully") {
                      Get.offNamed(AppRoutes.startScreen);
                    }
                  }
                },
                text: '変更する',
                backgroundColor: AppColors.primary,
              );
            }),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _currentPwdController.dispose();
    _newPwdController.dispose();
    _confirmPwdController.dispose();
    super.dispose();
  }
}
