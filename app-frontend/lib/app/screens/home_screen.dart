import 'package:lips_app/app/utils/app_colors.dart';
import 'package:lips_app/app/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeScreen extends StatelessWidget {
  HomeScreen({super.key});

  final RxString dropdownValue = '-'.obs;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.close, color: AppColors.whiteColor),
          onPressed: () {
            Get.offAllNamed(AppRoutes.login);
          },
        ),
        title: const Text('VLS Operation'),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'デバイス情報',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: WidgetStatePropertyAll(
                  AppColors.primary.withValues(alpha: 0.5),
                ),
                columns: const [
                  DataColumn(label: Text('名称')),
                  DataColumn(label: Text('表示ID')),
                  DataColumn(label: Text('割当作業')),
                  DataColumn(label: Text('充電[%]')),
                  DataColumn(label: Text('状態')),
                  DataColumn(label: Text('作業時間')),
                ],
                rows: [
                  DataRow(
                    cells: [
                      DataCell(
                        Center(
                          child: Text('kato_test', textAlign: TextAlign.center),
                        ),
                      ),
                      DataCell(Center(child: Text('0000'))),
                      DataCell(
                        Obx(
                          () => DropdownButton<String>(
                            items: [
                              DropdownMenuItem(value: '-', child: Text('-')),
                              DropdownMenuItem(
                                value: 'TEST',
                                child: Text('TEST'),
                              ),
                            ],
                            onChanged: (value) {
                              dropdownValue.value = value!;
                            },
                            value: dropdownValue.value,
                          ),
                        ),
                      ),
                      DataCell(Center(child: Text('70'))),
                      DataCell(Center(child: Text('未接続'))),
                      DataCell(Center(child: Text('-'))),
                    ],
                  ),
                ],
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                '作業情報',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                headingRowColor: WidgetStatePropertyAll(
                  AppColors.primary.withValues(alpha: 0.5),
                ),

                columns: const [
                  DataColumn(label: Text('作業名')),
                  DataColumn(label: Text('グループ番号')),
                  DataColumn(label: Text('割当デバイス数')),
                  DataColumn(label: Text('作業別進入可能エリア数')),
                  DataColumn(label: Text('経過時間')),
                ],
                rows: [
                  DataRow(
                    cells: [
                      DataCell(
                        Center(
                          child: Text('TEST', textAlign: TextAlign.center),
                        ),
                      ),
                      DataCell(
                        onTap: () {
                          Get.toNamed(AppRoutes.startScreen);
                        },
                        Center(child: Text('0')),
                      ),
                      DataCell(Center(child: Text('0'))),
                      DataCell(Center(child: Text('3'))),
                      DataCell(Center(child: Text('00:01:07.000'))),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
