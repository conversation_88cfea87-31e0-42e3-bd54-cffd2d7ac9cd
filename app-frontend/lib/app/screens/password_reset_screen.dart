import 'package:lips_app/app/utils/app_colors.dart';
import 'package:lips_app/app/utils/widgets/custom_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../controllers/password_reset_controller.dart';
import '../routes/app_routes.dart';
import '../utils/widgets/custom_label_text_field.dart';

class PasswordResetScreen extends StatelessWidget {
  PasswordResetScreen({super.key});
  final TextEditingController emailController = TextEditingController();
  final ResetPasswordController resetPasswordController = Get.find<ResetPasswordController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(leading: SizedBox.shrink(), title: const Text('パスワード再設定')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.all(20.w),
              child: Text(
                "登録されているメールアドレス宛に\nパスワード再設定用のURLをお送りします。",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(height: 50.h),
            LabeledTextField(
              controller: emailController,
                label: 'メールアドレス'
            ),
            SizedBox(height: 80.h),
            GetBuilder<ResetPasswordController>(
              builder: (controller) {
                return Visibility(
                  visible: controller.isLoading == false,
                  replacement: Center(
                    child: Container(
                      height: 70.h,
                      width: 0.8.sw,
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10)
                      ),
                        child: Center(child: CircularProgressIndicator(color: Colors.white,))
                    ),
                  ),
                  child: CustomButtonPrimary(
                    onPressed: _forgetPasswordButton,
                    text: '送信',
                    backgroundColor: AppColors.primary,
                  ),
                );
              }
            ),
          ],
        ),
      ),
    );
  }

  void _forgetPasswordButton() async{
    bool isSuccess = await resetPasswordController.requestPasswordReset(emailController.text);
    if(isSuccess){
      Get.toNamed(AppRoutes.passwordResetConfirmation);
    }
    else{

    }

  }
}
