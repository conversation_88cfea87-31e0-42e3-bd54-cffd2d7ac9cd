import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/login_controller.dart';
import '../utils/app_colors.dart';
import '../utils/widgets/custom_primary_button.dart';
import '../routes/app_routes.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  bool _obscureText = true;
  final TextEditingController _loginIdController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  final LoginController _loginController = Get.put(LoginController());

  /// ✅ Validate email format
  bool _isValidEmail(String email) {
    return GetUtils.isEmail(email);
  }

  /// ✅ Validate password length
  bool _isValidPassword(String password) {
    return password.length >= 8;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('ログイン画面')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),
            TextField(
              controller: _loginIdController,
              decoration: InputDecoration(
                labelText: 'ログインID',
                border: OutlineInputBorder(),
                hintText: 'ログインIDを入力してください',
              ),
            ),
            SizedBox(height: 20.h),
            TextField(
              controller: _passwordController,
              obscureText: _obscureText,
              decoration: InputDecoration(
                labelText: 'パスワード',
                border: OutlineInputBorder(),
                hintText: 'パスワードを入力してください',
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureText ? Icons.visibility_off : Icons.visibility,
                    color: AppColors.blueColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureText = !_obscureText;
                    });
                  },
                ),
              ),
            ),
            SizedBox(height: 40.h),
            Obx(() {
              if (_loginController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              return CustomButtonPrimary(
                onPressed: () async {
                  final email = _loginIdController.text.trim();
                  final password = _passwordController.text;

                  // ✅ Frontend validation
                  if (email.isEmpty || password.isEmpty) {
                    Get.snackbar('エラー', 'メールとパスワードを入力してください',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.redAccent,
                        colorText: Colors.white);
                    return;
                  }

                  if (!_isValidEmail(email)) {
                    Get.snackbar('メール形式エラー', '正しいメール形式を入力してください',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.redAccent,
                        colorText: Colors.white);
                    return;
                  }

                  if (!_isValidPassword(password)) {
                    Get.snackbar('パスワードエラー', 'パスワードは8文字以上で入力してください',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.redAccent,
                        colorText: Colors.white);
                    return;
                  }

                  // ✅ Login API call
                  await _loginController.login(email, password);

                  if (_loginController.errorMessage.value != null) {
                    Get.snackbar('ログインに失敗しました',
                        _loginController.errorMessage.value ?? 'エラーが発生しました',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.redAccent,
                        colorText: Colors.white);
                  } else {
                    Get.snackbar('成功', 'ログインに成功しました',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.green,
                        colorText: Colors.white);

                    Get.offNamed(AppRoutes.changePassword);
                  }
                },
                text: 'ログイン',
              );
            }),
            SizedBox(height: 10.h),
            Align(
              alignment: Alignment.center,
              child: TextButton(
                onPressed: () {
                  Get.toNamed(AppRoutes.resetPassword);
                },
                child: Text(
                  "パスワードを忘れた場合はこちら",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.blueColor,
                    decoration: TextDecoration.underline,
                    decorationColor: AppColors.blueColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
