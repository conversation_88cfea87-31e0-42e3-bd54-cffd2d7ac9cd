import 'package:lips_app/app/utils/app_colors.dart';
import 'package:lips_app/app/utils/widgets/custom_primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../controllers/confirm_forget_password_controller.dart';
import '../routes/app_routes.dart';
import '../utils/widgets/custom_label_text_field.dart';

class PasswordResetConfirmationScreen extends StatefulWidget {
  PasswordResetConfirmationScreen({super.key});

  @override
  _PasswordResetConfirmationScreenState createState() =>
      _PasswordResetConfirmationScreenState();
}

class _PasswordResetConfirmationScreenState extends State<PasswordResetConfirmationScreen> {
  bool _obscureTextNew = true;
  bool _obscureTextConfirm = true;

  final TextEditingController emailController = TextEditingController();
  final TextEditingController otpController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  RxString email = ''.obs; // observable email

  final ConfirmForgetPasswordController confirmForgetPasswordController = Get.find<ConfirmForgetPasswordController>();

  @override
  void initState() {
    super.initState();
    loadData();
  }
  Future<void> loadData() async {
    final prefs = await SharedPreferences.getInstance();
    final storedEmail = prefs.getString('email') ?? '';
    email.value = storedEmail;
    emailController.text = storedEmail;
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: AppBar(leading: SizedBox.shrink(), title: const Text('パスワード再設定')),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 50),
              Text(
                "パスワードリセット",
                style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20.h),
              Text(
                "メールで受け取ったOTPと新しいパスワード\nを入力してください",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14.sp),
              ),
              SizedBox(height: 50.h),
              // メールアドレス field
              TextField(
                controller: emailController,
                decoration: InputDecoration(
                  labelText: 'メールアドレス',
                  border: OutlineInputBorder(),
                  hintText: 'メールアドレスを入力してください',  // Placeholder in Japanese
                ),
              ),
              SizedBox(height: 20.h),
              // ワンタイムパスワード (OTP) field
              TextField(
                controller: otpController,
                decoration: InputDecoration(
                  labelText: 'ワンタイムパスワード (OTP)',
                  border: OutlineInputBorder(),
                  hintText: 'メールで受け取ったOTPを入力',  // Placeholder in Japanese
                ),
              ),
              SizedBox(height: 20.h),
              // 新しいパスワード field with eye icon
              TextField(
                controller: newPasswordController,
                obscureText: _obscureTextNew,
                decoration: InputDecoration(
                  labelText: '新しいパスワード',
                  border: OutlineInputBorder(),
                  hintText: '新しいパスワードを入力してください',
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureTextNew ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.blueColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureTextNew = !_obscureTextNew;
                      });
                    },
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              // パスワード確認 field with eye icon
              TextField(
                controller: confirmPasswordController,
                obscureText: _obscureTextConfirm,
                decoration: InputDecoration(
                  labelText: 'パスワード確認',
                  border: OutlineInputBorder(),
                  hintText: '新しいパスワードを再入力してください',
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureTextConfirm ? Icons.visibility_off : Icons.visibility,
                      color: AppColors.blueColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureTextConfirm = !_obscureTextConfirm;
                      });
                    },
                  ),
                ),
              ),
              SizedBox(height: 80.h),
              // Reset password button

              GetBuilder<ConfirmForgetPasswordController>(
                  builder: (controller) {
                    return Visibility(
                      visible: controller.isLoading == false,
                      replacement: Center(
                        child: Container(
                            width: 1.sw,
                            height: 45.h,
                            decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            child: Center(child: CircularProgressIndicator(color: Colors.white,))
                        ),
                      ),
                      child: CustomButtonPrimary(
                        onPressed: _forgetPasswordButton,
                        text: "パスワードをリセット",
                        backgroundColor: AppColors.primary,
                        fontSize: 18.sp,
                      ),
                    );
                  }
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _forgetPasswordButton() async {
    bool isSuccess = await confirmForgetPasswordController.confirmForgetPasswordApi(emailController.text, otpController.text, newPasswordController.text);
    if (isSuccess) {
      Get.offAllNamed(AppRoutes.login);
    } else {

    }   // Handle error
  }
}