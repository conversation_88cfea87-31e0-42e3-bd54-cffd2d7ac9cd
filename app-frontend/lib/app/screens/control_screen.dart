import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lips_app/app/routes/app_routes.dart';
import 'package:lips_app/app/utils/app_colors.dart';
import 'package:lips_app/app/controllers/control_lips_work_info_controller.dart';
import 'package:lips_app/app/utils/widgets/custom_primary_button.dart';

class ControlScreen extends StatelessWidget {
  ControlScreen({super.key});

  final ControlLipsWorkInfoController controller = Get.find<ControlLipsWorkInfoController>();
  final RxBool isOn = false.obs;
  RxInt index = 1.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const SizedBox.shrink(),
        title: const Text('ControlPage'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Obx(() {
          final info = controller.workInfo;
          final isLoading = controller.isLoading;

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 20.h),

                GestureDetector(
                  onTap: () {
                    if (index.value == 3) {
                      index.value = 1;
                    } else {
                      index.value++;
                    }
                  },
                  child: Obx(() => _getLabelWidget(index.value)),
                ),

                Align(
                  alignment: Alignment.bottomRight,
                  child: Text(
                    '※数値をタップして表示切替',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                ),

                SizedBox(height: 10.h),

                LabeledBox(
                  label: 'アラートステータス',
                  value: isLoading
                      ? 'サーバ\n応答待ち'
                      : statusText(info?.status ?? ''),
                  color: getStatusColor(info?.status ?? ''),
                  isLarge: true,
                ),

                SizedBox(height: 20.h),

                Obx(() {
                  final enabled = !controller.isLoading &&
                      controller.workInfo != null &&
                      buttonEnable(controller.workInfo!.status) &&
                      !isOn.value;

                  return CustomButtonPrimary(
                    onPressed: enabled
                        ? () {
                      final status = controller.workInfo?.status ?? '';
                      if (status.toLowerCase() == "waiting") {
                        Get.dialog(
                          AlertDialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            content: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  '「作業終了」を依頼し、\n許可を待ちしております。',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  '許可がされていないうちに\n下記のボタンを通じて、\nキャンセルができます。',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(fontSize: 14),
                                ),
                                const SizedBox(height: 20),
                                ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                  onPressed: () {
                                    Get.back(); // close dialog
                                  },
                                  child: const Text(
                                    'キャンセル',
                                    style: TextStyle(color: Colors.white, fontSize: 16),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          barrierDismissible: false,
                        );
                      } else {
                        Get.toNamed(AppRoutes.home);
                      }
                    }
                        : null,
                    text: '作業終了',
                    backgroundColor: enabled ? Colors.red : AppColors.greyColor,
                  );
                }),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _getLabelWidget(int index) {
    switch (index) {
      case 1:
        return LabeledBox(
          label: '作業名',
          value: controller.isLoading ? '- - - -' : controller.workInfo?.workName ?? '',
        );
      case 2:
        return LabeledBox(
          label: '作業名',
          value: controller.isLoading ? '- - - -' : controller.workInfo?.displayDeviceId ?? '',
        );
      default:
        return LabeledBox(
          label: '作業名',
          value: controller.isLoading ? '- - - -' : controller.workInfo?.battery ?? '',
        );
    }
  }

  String statusText(String status) {
    if (status.toLowerCase() == "waiting") {
      return "アラート\n無し";
    } else if (status.toLowerCase() == "areaChange") {
      return "エリア変更";
    } else if (status.toLowerCase() == "unstableLocation") {
      return "位置情報不安定";
    } else if (status.toLowerCase() == "prohibitedAreaApproach") {
      return "進入禁止エリア接近";
    } else if (status.toLowerCase() == "prohibitedAreaEnter") {
      return "進入禁止エリア進入";
    } else {
      return "アラート無し";
    }
  }

  Color getStatusColor(String status) {
    if (status.toLowerCase() == "waiting") {
      return Colors.grey;
    } else if (status.toLowerCase() == "areaChange") {
      return Colors.lightBlue;
    } else if (status.toLowerCase() == "unstableLocation") {
      return Colors.purple;
    } else if (status.toLowerCase() == "prohibitedAreaApproach") {
      return Colors.yellow;
    } else if (status.toLowerCase() == "prohibitedAreaEnter") {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }

  bool buttonEnable(String status) {
    return status.toLowerCase() == "waiting"; // ✅ Enable for noalert only
  }
}

class LabeledBox extends StatelessWidget {
  final String label;
  final String value;
  final bool isLarge;
  final Color? color;

  const LabeledBox({
    super.key,
    required this.label,
    required this.value,
    this.color,
    this.isLarge = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 16.sp)),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black, width: 3.w),
            color: color ?? Colors.white,
          ),
          child: Text(
            value,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w400),
          ),
        ),
      ],
    );
  }
}
