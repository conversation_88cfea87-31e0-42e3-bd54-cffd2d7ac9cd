import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lips_app/app/routes/app_routes.dart';
import 'package:lips_app/app/utils/widgets/custom_primary_button.dart';
import 'package:lips_app/app/utils/widgets/custom_label_text_field.dart';
import 'package:lips_app/app/controllers/start_business_operator_masters_controller.dart';

class StartScreen extends StatelessWidget {
  StartScreen({super.key});

  final StartBusinessOperatorMastersController _controller = Get.find<StartBusinessOperatorMastersController>();

  final TextEditingController domainController = TextEditingController();
  final TextEditingController deviceIdController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const SizedBox.shrink(),
        title: const Text('StartPage'),
        backgroundColor: Colors.orange,
      ),
      body: Obx(() {
        if (_controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_controller.errorMessage.isNotEmpty) {
          return Center(child: Text(_controller.errorMessage.value));
        }

        // Set values if data exists
        if (_controller.businessOperators.isNotEmpty) {
          domainController.text = _controller.businessOperators.first.domainName;
          deviceIdController.text = _controller.businessOperators.first.displayDeviceId;
        }

        return Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 20.h),
              LabeledTextField(
                controller: domainController,
                label: 'ドメイン名',
                isReadOnly: true,
              ),
              LabeledTextField(
                controller: deviceIdController,
                label: 'デバイスID',
                isReadOnly: true,
              ),
              SizedBox(height: 40.h),
              CustomButtonPrimary(
                onPressed: () {
                  Get.toNamed(AppRoutes.control);
                },
                text: '作業開始',
              ),
            ],
          ),
        );
      }),
    );
  }
}

