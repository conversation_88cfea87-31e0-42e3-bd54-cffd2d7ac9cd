class UserModel {
  final String token;
  final String refreshToken;
  final String email;
  final String fullName;

  UserModel({
    required this.token,
    required this.refreshToken,
    required this.email,
    required this.fullName,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      token: json['Token'],
      refreshToken: json['RefreshToken'],
      email: json['Email'],
      fullName: json['FullName'],
    );
  }
}
