class LipsWorkInfo {
  final String workName;
  final String displayDeviceId;
  final String status;
  final String battery;

  LipsWorkInfo({
    required this.workName,
    required this.displayDeviceId,
    required this.status,
    required this.battery,
  });

  factory LipsWorkInfo.fromJson(Map<String, dynamic> json) {
    return LipsWorkInfo(
      workName: json['work_name'] ?? '',
      displayDeviceId: json['display_device_id'] ?? '',
      status: json['status'] ?? '',
      battery: json['battery'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'work_name': workName,
      'display_device_id': displayDeviceId,
      'status': status,
      'battery': battery,
    };
  }
}
