class BusinessOperatorMaster {
  final String domainName;
  final String displayDeviceId;

  BusinessOperatorMaster({required this.domainName, required this.displayDeviceId});

  // Factory constructor to create a BusinessOperatorMaster from JSON
  factory BusinessOperatorMaster.fromJson(Map<String, dynamic> json) {
    return BusinessOperatorMaster(
      domainName: json['domain_name'],
      displayDeviceId: json['display_device_id'],
    );
  }

  // Method to convert the BusinessOperatorMaster to a JSON
  Map<String, dynamic> toJson() {
    return {
      'domain_name': domainName,
      'display_device_id': displayDeviceId,
    };
  }
}
