import 'package:get/get.dart';

// Import your screens here (adjust the paths if needed)
import 'package:lips_app/app/screens/control_screen.dart';
import 'package:lips_app/app/screens/home_screen.dart';
import 'package:lips_app/app/screens/start_screen.dart';

import '../screens/change_pwd_screen.dart';
import '../screens/login_screen.dart';
import '../screens/password_reset_confirmation_screen.dart';
import '../screens/password_reset_screen.dart';

class AppRoutes {
  // Route names as constants
  static const String splash = '/';
  static const String login = '/login-screen';
  static const String home = '/home-screen';
  static const String control = '/control-screen';
  static const String startScreen = '/start-screen';
  static const String changePassword = '/change-password';
  static const String resetPassword = '/reset-password-screen';
  static const String passwordResetConfirmation = '/password-reset-confirmation-screen';

  // List of GetPages for routing
  static final List<GetPage<dynamic>> getPages = [
    // GetPage(name: splash, page: () => const StartScreen()),
    GetPage(name: splash, page: () => StartScreen()),
    GetPage(name: login, page: () => const LoginScreen()),
    // GetPage(name: home, page: () => const HomeScreen()),
    GetPage(name: control, page: () => ControlScreen()),
    GetPage(name: changePassword, page: () => const ChangePwdScreen()),
    GetPage(name: resetPassword, page: () => PasswordResetScreen()),
    GetPage(name: passwordResetConfirmation, page: () =>  PasswordResetConfirmationScreen()),
  ];
}
