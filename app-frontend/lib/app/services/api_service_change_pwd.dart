import 'dart:convert';
import 'dart:io';
import 'package:http/io_client.dart';
import 'package:get_storage/get_storage.dart';

class ApiServiceChangePwd {
  final String baseUrl = "https://********:7185/api/auth";
  final storage = GetStorage();

  IOClient getHttpClient() {
    final ioc = HttpClient()
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return IOClient(ioc);
  }

  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    // Retrieve the token from storage
    final storedToken = storage.read('token');
    print('Retrieved Token from storage: $storedToken');

    if (storedToken == null || storedToken.isEmpty) {
      return {
        "IsSuccess": false,
        "Message": "User not authenticated. Token is null or empty."
      };
    }

    final url = Uri.parse('$baseUrl/change-password');

    final headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $storedToken",
    };

    final body = jsonEncode({
      "CurrentPassword": currentPassword,
      "NewPassword": newPassword,
      "ConfirmPassword": confirmPassword,
    });

    try {
      final client = getHttpClient();
      final response = await client.post(
        url,
        headers: headers,
        body: body,
      );

      print("Response Status Code: ${response.statusCode}");
      print("Response Body: ${response.body}");

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return {
          "IsSuccess": false,
          "Message": "Failed to change password. Status code: ${response.statusCode}"
        };
      }
    } catch (e) {
      return {
        "IsSuccess": false,
        "Message": "Error occurred: $e"
      };
    }
  }
}
