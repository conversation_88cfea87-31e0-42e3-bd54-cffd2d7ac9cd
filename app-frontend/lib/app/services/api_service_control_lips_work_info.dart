import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/control_lips_work_info.dart';

class ControlLipsWorkInfoService {
  final String baseUrl = "https://********:7185/api/v1/lips-work-info/work";

  Future<LipsWorkInfo?> fetchWorkInfo() async {
    final uri = Uri.parse(baseUrl);
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('accessToken') ?? '';

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);

        if (jsonResponse['IsSuccess'] == true) {
          final data = jsonResponse['Data'];
          if (data != null && data is Map<String, dynamic>) {

            await prefs.setString("work_name", data['work_name']);
            await prefs.setString("display_device_id", data['display_device_id']);
            await prefs.setString("status", data['status']);
            await prefs.setString("battery", data['battery']);
            return LipsWorkInfo.fromJson(data);
          } else {
            print("Invalid or missing 'Data' in response JSON");
          }
        } else {
          print("API returned IsSuccess = false");
        }
      } else {
        print("Request failed with status: ${response.statusCode}");
      }
    } catch (e) {
      print("Error fetching work info: $e");
    }

    return null;
  }
}
