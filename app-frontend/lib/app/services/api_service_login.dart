import 'dart:convert';
import 'dart:io';
import 'package:http/io_client.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/user_model.dart';

class ApiService {
  static const String baseUrl = "https://********:7185/api";
  HttpClient getHttpClient() {
    final client = HttpClient()
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    return client;
  }

  Future<UserModel?> login(String email, String password) async {
    final url = Uri.parse('$baseUrl/auth/login');
    final ioClient = IOClient(getHttpClient());

    final response = await ioClient.post(
      url,
      headers: {
        "Content-Type": "application/json",
      },
      body: jsonEncode({
        "Email": email,
        "Password": password,
      }),
    );

    if (response.statusCode == 200) {
      final body = jsonDecode(response.body);
      print(response.body);
      if (body['IsSuccess'] == true) {
       UserModel user = UserModel.fromJson(body['Data']);
       final prefs = await SharedPreferences.getInstance();
        prefs.setString('accessToken', user.token);
        prefs.setString('refreshToken', user.refreshToken);
        prefs.setString('userEmail', user.email);
        prefs.setString('userFullName', user.fullName);
        return user;
      } else {
        throw Exception('Login failed: ${body['Message'] ?? 'Unknown error'}');
      }
    } else {
      throw Exception('Failed to connect to the server (status ${response.statusCode})');
    }
  }
}
