import 'dart:convert';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ForgetPasswordService{
  static String baseUrl = 'https://10.0.2.2:7185/api/v1/userapi';
  static final storage = GetStorage();

  static Future<Map<String, dynamic>?> forgetPassword(String email) async {
    final headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };

    final response = await http.post(
      Uri.parse("$baseUrl/forgot-password"),
      headers: headers,
      body: jsonEncode({"email": email}),
    );
    if (response.statusCode == 200) {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString('email', email);

      final body = jsonDecode(response.body);
      if (body['IsSuccess'] == true) {
        final result = {
          "IsSuccess": true,
          "Message": body['Data'],
        };
        return result;
      } else {
        throw Exception('Login failed: ${body['Message'] ?? 'Unknown error'}');
      }
    }
    else {
      throw Exception('Failed to connect to the server (status ${response.statusCode})');
    }
  }

  static Future<Map<String, dynamic>?> confirmForgetPassword(
      String email,
      String otp,
      String newPassword)
  async {
    final headers = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };

    final response = await http.post(
      Uri.parse("$baseUrl/reset-password"),
      headers: headers,
      body: jsonEncode({
        "Otp": otp,
        "Email": email,
        "Password": newPassword,
        "ConfirmPassword": newPassword
      }),
    );
    if (response.statusCode == 200) {
      final body = jsonDecode(response.body);
      if (body['IsSuccess'] == true) {
        final result = {
          "IsSuccess": true,
          "Message": body['Data'],
        };
        return result;
      } else {
        throw Exception('Login failed: ${body['Message'] ?? 'Unknown error'}');
      }
    }
    else {
      throw Exception('Failed to connect to the server (status ${response.statusCode})');
    }
  }
}