import 'dart:convert';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:lips_app/app/data/models/start_business-operator-masters.dart';

class StartBusinessOperatorMastersService {
  static const String _baseUrl = 'https://********:7185/api/v1/business-operator-masters/device';

  static Future<List<BusinessOperatorMaster>?> fetchBusinessOperatorMasters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('accessToken') ?? '';
      final response = await http.get(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> data = jsonDecode(response.body);

        if (data['IsSuccess'] == true && data['Data'] != null) {
          final List<dynamic> list = data['Data'];
          return list.map((e) => BusinessOperatorMaster.fromJson(e)).toList();
        } else {
          throw Exception('API call successful but IsSuccess=false or Data=null');
        }
      } else {
        throw Exception('Failed to load data. Status: ${response.statusCode}');
      }
    } catch (e) {
      print('StartBusinessOperatorMastersService error: $e');
      return null;
    }
  }
}