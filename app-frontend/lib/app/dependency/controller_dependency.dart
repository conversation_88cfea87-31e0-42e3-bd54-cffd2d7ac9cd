
import 'package:get/get.dart';

import '../controllers/confirm_forget_password_controller.dart';
import '../controllers/control_lips_work_info_controller.dart';
import '../controllers/password_reset_controller.dart';
import '../controllers/start_business_operator_masters_controller.dart';
import '../services/api_service_start_business_operator_masters.dart';

class ControllerDependency extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(() => ResetPasswordController());
    Get.lazyPut(() => ConfirmForgetPasswordController());
    Get.lazyPut(() => StartBusinessOperatorMastersController());
    Get.lazyPut(() => ControlLipsWorkInfoController());
  }

}