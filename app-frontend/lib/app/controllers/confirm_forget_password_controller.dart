import 'package:get/get.dart';

import '../services/forget_password_service.dart';

class ConfirmForgetPasswordController extends GetxController{
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  String? error;

  Future<bool> confirmForgetPasswordApi(String email, String otp, String newPassword) async {
    bool isSuccess = false;
    _isLoading = true;
    update();

    try {
      final otpSendStatus = await ForgetPasswordService.confirmForgetPassword(email, otp, newPassword);
      if(otpSendStatus != null && otpSendStatus["IsSuccess"] == true){
        isSuccess = true;
        update();
        error = null;
      }
      else{
        error = "Something is wrong";
      }
    }
    catch(e){

    }

    _isLoading = false;
    update();

    return isSuccess;
  }

}