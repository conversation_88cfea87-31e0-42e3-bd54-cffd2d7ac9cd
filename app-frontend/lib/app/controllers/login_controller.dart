import 'package:get/get.dart';
import '../services/api_service_login.dart';
import '../data/models/user_model.dart';

class LoginController extends GetxController {
  final ApiService _apiService = ApiService();

  var isLoading = false.obs;
  var errorMessage = RxnString();
  UserModel? user;

  Future<void> login(String email, String password) async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      user = await _apiService.login(email, password);
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
}

