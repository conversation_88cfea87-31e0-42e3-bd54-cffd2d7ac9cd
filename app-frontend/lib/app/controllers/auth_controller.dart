import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/user_model.dart';

class AuthController {
  static const String _baseUrl = 'https://10.0.2.2:7185/api';
  static UserModel? _user;

  static Future<void> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/auth/login'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'Email': email, 'Password': password}),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body)['Data'];
      _user = UserModel.fromJson(data);

      final prefs = await SharedPreferences.getInstance();
      prefs.setString('accessToken', _user!.token);
      prefs.setString('refreshToken', _user!.refreshToken);
      prefs.setString('userEmail', _user!.email);
      prefs.setString('userFullName', _user!.fullName);
    } else {
      throw Exception('Login failed');
    }
  }

  static Future<void> changePassword(String currentPassword, String newPassword) async {
    final prefs = await SharedPreferences.getInstance();
    final accessToken = prefs.getString("accessToken");
    final response = await http.post(
      Uri.parse('$_baseUrl/auth/change-password'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode({
        'CurrentPassword': currentPassword,
        'NewPassword': newPassword,
        'ConfirmPassword': newPassword,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Password change failed');
    }
  }

  static Future<void> forgotPassword(String email) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/v1/userapi/forgot-password'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${_user?.token}',
      },
      body: jsonEncode({'Email': email}),
    );

    if (response.statusCode != 200) {
      throw Exception('Password reset request failed');
    }
  }

  static Future<void> resetPassword(String otp, String email, String newPassword) async {
    final response = await http.post(
      Uri.parse('$_baseUrl/v1/userapi/reset-password'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${_user?.token}',
      },
      body: jsonEncode({
        'Otp': otp,
        'Email': email,
        'Password': newPassword,
        'ConfirmPassword': newPassword,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Password reset failed');
    }
  }

  // Accessors
  static String? get accessToken => _user?.token;
  static String? get refreshToken => _user?.refreshToken;
  static String? get userEmail => _user?.email;
  static String? get userFullName => _user?.fullName;
  static UserModel? get user => _user;
}
