import 'package:get/get.dart';
import 'package:lips_app/app/data/models/start_business-operator-masters.dart';
import '../services/api_service_start_business_operator_masters.dart';

class StartBusinessOperatorMastersController extends GetxController {
  var businessOperators = <BusinessOperatorMaster>[].obs;
  var isLoading = true.obs;
  var errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchBusinessOperatorMasters();
  }

  Future<void> fetchBusinessOperatorMasters() async {
    try {
      isLoading(true);
      errorMessage.value = ''; // Clear previous errors
      var response = await StartBusinessOperatorMastersService.fetchBusinessOperatorMasters();
      if (response != null && response.isNotEmpty) {
        businessOperators.assignAll(response);
      } else {
        errorMessage('データの読み込みに失敗しました');
      }
    } catch (e) {
      errorMessage('エラー: $e');
    } finally {
      isLoading(false);
    }
  }
}

