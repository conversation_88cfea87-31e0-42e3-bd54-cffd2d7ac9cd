import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

class ChangePasswordController extends GetxController {
  var isLoading = false.obs;
  var message = ''.obs;

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      isLoading.value = true;
      message.value = '';

      if (newPassword.isEmpty || currentPassword.isEmpty) {
        message.value = '新しいパスワードまたは現在のパスワードが空です';
        return;
      }

      await AuthController.changePassword(
        currentPassword,
        newPassword,
      );

      message.value = 'Password changed successfully';
    } catch (e) {
      message.value = 'パスワード変更に失敗しました。${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }
}
