import 'package:get/get.dart';
import '../services/forget_password_service.dart';

class ResetPasswordController extends GetxController{
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  String? error;

  Future<bool> requestPasswordReset(String email) async {
    bool isSuccess = false;
    _isLoading = true;
    update();

    try {
      final otpSendStatus = await ForgetPasswordService.forgetPassword(email);
      if(otpSendStatus != null && otpSendStatus["IsSuccess"] == true){
        isSuccess = true;
        update();
        error = null;
      }
      else{
        error = "Something is wrong";
      }
    }
    catch(e){

    }

    _isLoading = false;
    update();

    return isSuccess;
  }

}