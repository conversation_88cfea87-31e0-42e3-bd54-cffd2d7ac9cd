import 'package:get/get.dart';
import '../data/models/control_lips_work_info.dart';
import '../services/api_service_control_lips_work_info.dart';

class ControlLipsWorkInfoController extends GetxController {
  final ControlLipsWorkInfoService _service = ControlLipsWorkInfoService();

  final Rx<LipsWorkInfo?> _workInfo = Rx<LipsWorkInfo?>(null);
  final RxBool _isLoading = false.obs;

  LipsWorkInfo? get workInfo => _workInfo.value;
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    loadWorkInfo();
  }

  Future<void> loadWorkInfo() async {
    _isLoading.value = true;

    try {
      final fetchedWorkInfo = await _service.fetchWorkInfo();
      _workInfo.value = fetchedWorkInfo;
    } catch (e) {
      print('Error loading work info: $e');
      _workInfo.value = null;
    } finally {
      _isLoading.value = false;
    }
  }
}