// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../services/api_service_login.dart';
// import '../routes/app_routes.dart';
//
// class PasswordResetConfirmationController extends GetxController {
//   // Text controllers for the fields
//   final emailController = TextEditingController();
//   final otpController = TextEditingController();
//   final newPasswordController = TextEditingController();
//   final confirmPasswordController = TextEditingController();
//
//   // Loading state
//   var isLoading = false.obs;
//
//   // ApiService instance (inject or create)
//   final ApiService apiService = Get.find<ApiService>();
//
//   // Validate inputs before sending request
//   bool validateInputs() {
//     if (emailController.text.isEmpty) {
//       Get.snackbar('Error', 'メールアドレスを入力してください');
//       return false;
//     }
//     if (otpController.text.isEmpty) {
//       Get.snackbar('Error', 'OTPを入力してください');
//       return false;
//     }
//     if (newPasswordController.text.isEmpty) {
//       Get.snackbar('Error', '新しいパスワードを入力してください');
//       return false;
//     }
//     if (confirmPasswordController.text.isEmpty) {
//       Get.snackbar('Error', 'パスワード確認を入力してください');
//       return false;
//     }
//     if (newPasswordController.text != confirmPasswordController.text) {
//       Get.snackbar('Error', 'パスワードが一致しません');
//       return false;
//     }
//     return true;
//   }
//
//   // Call API to confirm password reset
//   Future<void> confirmReset() async {
//     if (!validateInputs()) return;
//
//     try {
//       isLoading.value = true;
//
//       final response = await apiService.confirmResetPassword(
//         emailController.text.trim(),
//         otpController.text.trim(),
//         newPasswordController.text.trim(),
//       );
//
//       isLoading.value = false;
//
//       if (response['IsSuccess'] == true) {
//         Get.snackbar('Success', 'パスワードがリセットされました');
//         Get.offAllNamed(AppRoutes.login);
//       } else {
//         final errors = response['Errors'] ?? ['パスワードリセットに失敗しました'];
//         Get.snackbar('Error', errors.join('\n'));
//       }
//     } catch (e) {
//       isLoading.value = false;
//       Get.snackbar('Error', 'エラーが発生しました: $e');
//     }
//   }
//
//   @override
//   void onClose() {
//     emailController.dispose();
//     otpController.dispose();
//     newPasswordController.dispose();
//     confirmPasswordController.dispose();
//     super.onClose();
//   }
// }
