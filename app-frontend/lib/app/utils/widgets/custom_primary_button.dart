import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../app_colors.dart';

class CustomButtonPrimary extends StatelessWidget {
  final String? text;
  final double? fontSize;
  final Color? textColor;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final double? width;
  final double? height;
  final double? borderRadius;
  const CustomButtonPrimary({
    super.key,
    this.text,
    this.onPressed,
    this.backgroundColor,
    this.width,
    this.height,
    this.fontSize,
    this.borderRadius,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColors.buttonPrimaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 25.r),
          ),
          fixedSize: Size(width ?? 0.8.sw, height ?? 70.h),
        ),
        child: Text(
          text!,
          style: TextStyle(
            fontSize: fontSize ?? 35.sp,
            color: textColor ?? AppColors.blackColor,
          ),
        ),
      ),
    );
  }
}
