import 'package:lips_app/app/utils/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LabeledTextField extends StatelessWidget {
  final String label;
  final String? hintText;
  final bool obscureText;
  final bool? isReadOnly;
  final TextEditingController? controller;

  const LabeledTextField({
    super.key,
    required this.label,
    this.obscureText = false,
    this.controller,
    this.isReadOnly = false,
    this.hintText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
        ),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          readOnly: isReadOnly!,
          decoration: InputDecoration(
            hintText: hintText ?? "",
            hintStyle: TextStyle(fontSize: 16.sp, color: AppColors.hintTextColor),
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 10.w),
          ),
        ),

      ],
    );
  }
}
