import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:lips_app/app/routes/app_routes.dart';
import 'package:lips_app/app/utils/app_colors.dart';
import 'package:lips_app/app/controllers/auth_controller.dart';

import 'app/dependency/controller_dependency.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  HttpOverrides.global = MyHttpOverrides();

  // await AuthController.restoreSession();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLoggedIn = AuthController.accessToken != null;

    return ScreenUtilInit(
      builder: (context, child) {
        return GetMaterialApp(
          initialBinding: ControllerDependency(),
          title: 'Lips App',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: AppColors.primary,
            scaffoldBackgroundColor: AppColors.whiteColor,
            appBarTheme: AppBarTheme(
              backgroundColor: AppColors.primary,
              centerTitle: true,
              titleTextStyle: TextStyle(
                color: AppColors.whiteColor,
                fontSize: 20.sp,
              ),
            ),
          ),
          initialRoute: isLoggedIn ? AppRoutes.home : AppRoutes.login,
          getPages: AppRoutes.getPages,
        );
      },
    );
  }
}
