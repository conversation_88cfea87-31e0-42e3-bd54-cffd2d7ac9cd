{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.4", "@reduxjs/toolkit": "^2.7.0", "@tailwindcss/vite": "^4.1.4", "async-mutex": "^0.5.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.501.0", "moment": "^2.30.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.0", "react-redux": "^9.2.0", "react-router": "^7.5.1", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.7", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}, "packageManager": "yarn@4.9.1"}