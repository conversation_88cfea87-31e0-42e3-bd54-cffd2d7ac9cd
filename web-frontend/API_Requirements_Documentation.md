# API Requirements Documentation

## Implementation Status

### ✅ **COMPLETED FRONTEND FEATURES:**

1. **User List with Device ID Integration**

   - ✅ Device column added to UserManagement table
   - ✅ Device assignment display with styling
   - ✅ `getDeviceInfo()` helper function implemented

2. **Device Dropdown with CRUD Actions**

   - ✅ Device dropdown in AddUserModal with device selection
   - ✅ Device dropdown in UpdateUserModal with device selection
   - ✅ Show/Add/Edit/Delete action buttons (Plus, Eye, Edit, Trash2 icons)
   - ✅ DeviceManagementModal component for full CRUD operations

3. **TypeScript Integration**
   - ✅ Device type imported and used correctly
   - ✅ User interface updated with device_id property
   - ✅ All compilation errors resolved

### ❌ **MISSING BACKEND API ENDPOINTS:**

## 1. Device Management APIs

### Create Device

- **Endpoint:** `POST /lipsapi/device/`
- **Purpose:** Create new devices from the frontend device management modal
- **Request Body:**

```json
{
  "name": "デバイス001",
  "deviceId": "DEV001",
  "charge": 85,
  "status": "active"
}
```

### Get Device by ID

- **Endpoint:** `GET /lipsapi/device/{id}/`
- **Purpose:** Fetch single device details for edit/view operations
- **Current Status:** Referenced in deviceApi but needs implementation

### Update Device

- **Endpoint:** `PUT /lipsapi/device/{id}/`
- **Purpose:** Update device name, battery charge, and status
- **Note:** Device ID should NOT be updatable after creation

### Delete Device

- **Endpoint:** `DELETE /lipsapi/device/{id}/`
- **Purpose:** Remove devices from the system
- **Current Status:** Referenced in frontend but needs backend implementation

## 2. User Device Assignment

### Update User API Enhancement

- **Endpoint:** `PUT /userapi/users/update/{id}/`
- **Required Change:** Add `device_id` field support
- **Purpose:** Allow assigning/unassigning devices to users

### Create User API Enhancement

- **Endpoint:** `POST /userapi/users/add/`
- **Required Change:** Add `device_id` field support
- **Purpose:** Allow device assignment during user creation

### Get Users API Enhancement

- **Endpoint:** `GET /userapi/users/list/`
- **Required Change:** Include `device_id` in response data
- **Purpose:** Display device assignments in user management table

## 3. Work Timer Management

### Reset Device Timer

- **Endpoint:** `POST /lipsapi/device/{device_id}/timer/reset/`
- **Purpose:** Reset device timer when work is assigned in Device Management screen
- **Business Logic:**
  - When work is assigned to a device, reset the device.work_time field to "00:00:00"
  - This provides a fresh start for tracking time for the newly assigned work
  - Updates both device.work_id and device.work_time fields

**Request Body:**

```json
{
  "work_id": 1
}
```

**Response:**

```json
{
  "success": true,
  "message": "Device timer reset successfully",
  "data": {
    "device_id": "DEVICE001",
    "work_id": 1,
    "work_time": "00:00:00",
    "reset_timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Device Work Time Calculation

- **Endpoint:** `GET /lipsapi/device/{device_id}/work-time/`
- **Purpose:** Calculate current work time for a device
- **Business Logic:**
  - Read device.work_time field (accumulated time from previous sessions)
  - Calculate elapsed time since work assignment (updated_at timestamp)
  - Return total time = device.work_time + elapsed_time_since_assignment

**Response:**

```json
{
  "success": true,
  "data": {
    "device_id": "DEVICE001",
    "work_id": 1,
    "accumulated_time": "01:15:00",
    "elapsed_since_assignment": "00:30:45",
    "total_work_time": "01:45:45",
    "work_assigned_at": "2024-01-15T10:30:00Z"
  }
}
```

### Active Works with Device Time Calculation

- **Endpoint:** `GET /lipsapi/works/active-works/time-calculation/`
- **Purpose:** Get all active works with calculated time for work list display
- **Enhancement:** Calculate total time across all devices assigned to each work

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "work_name": "Assembly Task A",
      "group_number": "GRP001",
      "assigned_devices": 3,
      "accessible_areas": 5,
      "time_elapsed": "02:45:30",
      "device_times": [
        {
          "device_id": "DEVICE001",
          "device_work_time": "01:45:45"
        },
        {
          "device_id": "DEVICE002",
          "device_work_time": "01:00:00"
        }
      ]
    }
  ]
}
```

## Database Schema Requirements

### Device Table Analysis

**Current Device Entity Structure (✅ Already Implemented):**

```
lips_device_masters table:
- id (Primary Key)
- display_device_id (Device identifier)
- device_name (Device name)
- work_time (String - accumulated work time)
- battery (Battery percentage)
- previous_alert_instruction
- signal_period
- created_at, updated_at (Timestamps)
- user_id (Foreign key - user assignment)
- work_id (Foreign key - work assignment)
- business_operator_id
- status (Device status)
```

**Key Findings:**

- ✅ Device entity already has `user_id` for user assignment
- ✅ Device entity already has `work_id` for work assignment
- ✅ Device entity already has `work_time` field for timer tracking
- ✅ Device entity has proper timestamp fields

### User Table Updates

- Add `device_id` field (foreign key to Device table)
- Ensure proper indexing for performance
- Handle null values for users without device assignment

### Work Timer Implementation Notes

**Device.work_time Field Usage:**

- Field Type: `string` (format: "HH:MM:SS")
- Purpose: Store accumulated work time for the device
- Business Logic:
  - Reset to "00:00:00" when new work is assigned
  - Updated periodically or on work completion
  - Used for total time calculations

**Timer Calculation Logic:**

1. **Reset Timer:** Set device.work_time = "00:00:00" when work assigned
2. **Calculate Current Time:** device.work_time + elapsed_since_updated_at
3. **Aggregate Work Time:** Sum all device times for work summary

## Implementation Priority

### High Priority (Core Functionality)

1. Device CRUD APIs - Required for device management modal
2. User device assignment APIs - Required for user-device relationships
3. Enhanced user list API - Required to display device assignments

### Medium Priority (Business Logic)

1. Work timer reset API - Required for timer management
2. Work time calculation APIs - Required for time display

## Frontend Integration Points

### DeviceManagementModal.tsx

- Already calls: `useCreateDeviceMutation`, `useUpdateDeviceMutation`, `useDeleteDeviceMutation`
- Needs: All device CRUD endpoints implemented

### UserManagement.tsx

- Already calls: `useGetAllDevicesQuery` for device lookup
- Already displays: Device assignments in table
- Needs: User APIs enhanced with device_id support

### AddUserModal.tsx & UpdateUserModal.tsx

- Already includes: Device dropdown and assignment logic
- Already calls: Device management functions
- Needs: User create/update APIs with device_id support

## API Response Format Standards

All APIs should follow the consistent response format:

```json
{
  "success": true,
  "data": {
    /* response data */
  },
  "message": "Optional success message"
}
```

For errors:

```json
{
  "success": false,
  "error": "Error message",
  "details": {
    /* optional error details */
  }
}
```

## Testing Checklist

### Device Management

- [ ] Create device with valid data
- [ ] Create device with duplicate deviceId (should fail)
- [ ] Update device name and battery
- [ ] Attempt to update deviceId (should fail)
- [ ] Delete device not assigned to user
- [ ] Delete device assigned to user (should handle gracefully)

### User Device Assignment

- [ ] Assign device to user during creation
- [ ] Assign device to existing user
- [ ] Unassign device from user
- [ ] Assign same device to multiple users (should prevent)
- [ ] User list displays device assignments correctly

### Work Timer Management

- [ ] Reset timer sets elapsed time to 00:00:00
- [ ] Time calculation includes creation time + reset periods
- [ ] Active works list shows calculated times
- [ ] Timer continues after reset

## Security Considerations

- Ensure proper authorization for device management (admin only)
- Validate device assignments don't conflict
- Sanitize all input data
- Use proper foreign key constraints
- Implement soft deletes for audit trails

---

**Note:** The frontend implementation is complete and ready. Once these backend APIs are implemented, the device management and user-device assignment functionality will be fully operational.
