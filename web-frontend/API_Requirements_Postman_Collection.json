{"info": {"name": "Notification Alert - Missing API Endpoints", "description": "API endpoints needed for Device Management and User Management with device assignment functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Device Management APIs", "description": "CRUD operations for device management", "item": [{"name": "Create Device", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"デバイス001\",\n  \"deviceId\": \"DEV001\",\n  \"charge\": 85,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", ""]}, "description": "Create a new device with name, deviceId, charge (battery), and status"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"デバイス001\",\n  \"deviceId\": \"DEV001\",\n  \"charge\": 85,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", ""]}}, "status": "Created", "code": 201, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"12345\",\n    \"display_device_id\": \"DEV001\",\n    \"device_name\": \"デバイス001\",\n    \"battery\": 85,\n    \"signal_period\": 1000,\n    \"user_id\": null,\n    \"work_id\": null,\n    \"status\": \"active\",\n    \"created_at\": \"2025-06-04T10:00:00Z\",\n    \"updated_at\": \"2025-06-04T10:00:00Z\"\n  }\n}"}]}, {"name": "Get Device by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/lipsapi/device/{{device_id}}/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "{{device_id}}", ""]}, "description": "Get a single device by its ID"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/lipsapi/device/12345/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "12345", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"12345\",\n    \"display_device_id\": \"DEV001\",\n    \"device_name\": \"デバイス001\",\n    \"battery\": 85,\n    \"signal_period\": 1000,\n    \"user_id\": \"67890\",\n    \"work_id\": 5,\n    \"status\": \"active\",\n    \"work_time\": \"2025-06-04T09:30:00Z\",\n    \"created_at\": \"2025-06-04T10:00:00Z\",\n    \"updated_at\": \"2025-06-04T10:00:00Z\"\n  }\n}"}]}, {"name": "Update Device", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"デバイス001 - 更新済み\",\n  \"charge\": 75,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/{{device_id}}/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "{{device_id}}", ""]}, "description": "Update device name, charge (battery), and status. DeviceId should not be updatable."}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"name\": \"デバイス001 - 更新済み\",\n  \"charge\": 75,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/12345/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "12345", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"12345\",\n    \"display_device_id\": \"DEV001\",\n    \"device_name\": \"デバイス001 - 更新済み\",\n    \"battery\": 75,\n    \"signal_period\": 1000,\n    \"user_id\": \"67890\",\n    \"work_id\": 5,\n    \"status\": \"active\",\n    \"updated_at\": \"2025-06-04T11:00:00Z\"\n  }\n}"}]}, {"name": "Delete Device", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/lipsapi/device/{{device_id}}/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "{{device_id}}", ""]}, "description": "Delete a device by its ID"}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/lipsapi/device/12345/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "12345", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"message\": \"Devi<PERSON> deleted successfully\"\n}"}]}]}, {"name": "User Device Assignment APIs", "description": "APIs for managing device assignments to users", "item": [{"name": "Update User with <PERSON><PERSON> Assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"田中太郎\",\n  \"username\": \"tanaka_taro\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"090-1234-5678\",\n  \"role_name\": \"user\",\n  \"company_name\": \"サンプル会社\",\n  \"notes\": \"メモ\",\n  \"device_id\": \"12345\"\n}"}, "url": {"raw": "{{base_url}}/userapi/users/update/{{user_id}}/", "host": ["{{base_url}}"], "path": ["userapi", "users", "update", "{{user_id}}", ""]}, "description": "Update user information including device assignment. The device_id field should be added to the existing user update API."}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"田中太郎\",\n  \"username\": \"tanaka_taro\",\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"090-1234-5678\",\n  \"role_name\": \"user\",\n  \"company_name\": \"サンプル会社\",\n  \"notes\": \"メモ\",\n  \"device_id\": \"12345\"\n}"}, "url": {"raw": "{{base_url}}/userapi/users/update/67890/", "host": ["{{base_url}}"], "path": ["userapi", "users", "update", "67890", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"status\": true,\n  \"message\": \"User updated successfully\",\n  \"data\": {\n    \"id\": 67890,\n    \"username\": \"tanaka_taro\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"田中太郎\",\n    \"phone_number\": \"090-1234-5678\",\n    \"company_name\": \"サンプル会社\",\n    \"notes\": \"メモ\",\n    \"device_id\": \"12345\",\n    \"role\": {\n      \"id\": 3,\n      \"name\": \"user\"\n    }\n  }\n}"}]}, {"name": "Create User with Device Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"佐藤花子\",\n  \"username\": \"sato_hanako\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"phone_number\": \"090-9876-5432\",\n  \"role\": 3,\n  \"company_name\": \"新会社\",\n  \"notes\": \"新規ユーザー\",\n  \"device_id\": \"12345\"\n}"}, "url": {"raw": "{{base_url}}/userapi/users/add/", "host": ["{{base_url}}"], "path": ["userapi", "users", "add", ""]}, "description": "Create new user with device assignment. The device_id field should be added to the existing user creation API."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"full_name\": \"佐藤花子\",\n  \"username\": \"sato_hanako\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"phone_number\": \"090-9876-5432\",\n  \"role\": 3,\n  \"company_name\": \"新会社\",\n  \"notes\": \"新規ユーザー\",\n  \"device_id\": \"12345\"\n}"}, "url": {"raw": "{{base_url}}/userapi/users/add/", "host": ["{{base_url}}"], "path": ["userapi", "users", "add", ""]}}, "status": "Created", "code": 201, "body": "{\n  \"status\": true,\n  \"message\": \"User created successfully\",\n  \"data\": {\n    \"id\": 99999,\n    \"username\": \"sato_hana<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"full_name\": \"佐藤花子\",\n    \"phone_number\": \"090-9876-5432\",\n    \"company_name\": \"新会社\",\n    \"notes\": \"新規ユーザー\",\n    \"device_id\": \"12345\",\n    \"role\": {\n      \"id\": 3,\n      \"name\": \"user\"\n    }\n  }\n}"}]}, {"name": "Get Users List (Updated with device_id)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/userapi/users/list/", "host": ["{{base_url}}"], "path": ["userapi", "users", "list", ""]}, "description": "Get all users with device_id field included in the response"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/userapi/users/list/", "host": ["{{base_url}}"], "path": ["userapi", "users", "list", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"status\": true,\n  \"data\": [\n    {\n      \"id\": 67890,\n      \"username\": \"tanaka_taro\",\n      \"email\": \"<EMAIL>\",\n      \"full_name\": \"田中太郎\",\n      \"phone_number\": \"090-1234-5678\",\n      \"company_name\": \"サンプル会社\",\n      \"notes\": \"メモ\",\n      \"device_id\": \"12345\",\n      \"role\": {\n        \"id\": 3,\n        \"name\": \"user\"\n      },\n      \"last_login\": \"2025-06-04T09:00:00Z\"\n    },\n    {\n      \"id\": 99999,\n      \"username\": \"sato_hanako\",\n      \"email\": \"<EMAIL>\",\n      \"full_name\": \"佐藤花子\",\n      \"phone_number\": \"090-9876-5432\",\n      \"company_name\": \"新会社\",\n      \"notes\": \"新規ユーザー\",\n      \"device_id\": null,\n      \"role\": {\n        \"id\": 3,\n        \"name\": \"user\"\n      },\n      \"last_login\": null\n    }\n  ]\n}"}]}]}, {"name": "Device Timer Management APIs", "description": "APIs for device timer management and work time calculations", "item": [{"name": "Reset Device Timer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"work_id\": 5\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/{{device_id}}/timer/reset/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "{{device_id}}", "timer", "reset", ""]}, "description": "Reset device work_time field to 00:00:00 when work is assigned"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"work_id\": 5\n}"}, "url": {"raw": "{{base_url}}/lipsapi/device/12345/timer/reset/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "12345", "timer", "reset", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"message\": \"Device timer reset successfully\",\n  \"data\": {\n    \"device_id\": \"12345\",\n    \"work_id\": 5,\n    \"work_time\": \"00:00:00\",\n    \"reset_timestamp\": \"2025-06-04T12:00:00Z\"\n  }\n}"}]}, {"name": "Get Device Work Time", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/lipsapi/device/{{device_id}}/work-time/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "{{device_id}}", "work-time", ""]}, "description": "Calculate current work time: device.work_time + elapsed_since_assignment"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/lipsapi/device/12345/work-time/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "device", "12345", "work-time", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"device_id\": \"12345\",\n    \"work_id\": 5,\n    \"accumulated_time\": \"01:15:00\",\n    \"elapsed_since_assignment\": \"00:30:45\",\n    \"total_work_time\": \"01:45:45\",\n    \"work_assigned_at\": \"2025-06-04T10:30:00Z\"\n  }\n}"}]}]}, {"name": "Work Time Management APIs", "description": "APIs for work time calculation and management", "item": [{"name": "Work Time Calculation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/lipsapi/works/{{work_id}}/time-calculation/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "{{work_id}}", "time-calculation", ""]}, "description": "Get work information with calculated time: current time (elapsed_time) + created_at time"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/lipsapi/works/5/time-calculation/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "5", "time-calculation", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": 5,\n    \"work_name\": \"作業001\",\n    \"group_number\": \"G001\",\n    \"created_at\": \"2025-06-04T09:00:00Z\",\n    \"last_reset_time\": \"2025-06-04T12:00:00Z\",\n    \"current_time\": \"2025-06-04T14:30:00Z\",\n    \"elapsed_time_since_creation\": \"05:30:00\",\n    \"elapsed_time_since_reset\": \"02:30:00\",\n    \"total_calculated_time\": \"07:30:00\",\n    \"assigned_devices\": 3,\n    \"accessible_areas\": 5,\n    \"status\": \"active\"\n  }\n}"}]}, {"name": "Get All Active Works with Time Calculation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/lipsapi/works/active-works/time-calculation/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "active-works", "time-calculation", ""]}, "description": "Get all active works with calculated time for work list time count functionality"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/lipsapi/works/active-works/time-calculation/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "active-works", "time-calculation", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": 5,\n      \"work_name\": \"作業001\",\n      \"group_number\": \"G001\",\n      \"created_at\": \"2025-06-04T09:00:00Z\",\n      \"last_reset_time\": \"2025-06-04T12:00:00Z\",\n      \"current_time\": \"2025-06-04T14:30:00Z\",\n      \"elapsed_time_since_creation\": \"05:30:00\",\n      \"elapsed_time_since_reset\": \"02:30:00\",\n      \"total_calculated_time\": \"07:30:00\",\n      \"assigned_devices\": 3,\n      \"accessible_areas\": 5,\n      \"status\": \"active\"\n    },\n    {\n      \"id\": 6,\n      \"work_name\": \"作業002\",\n      \"group_number\": \"G002\",\n      \"created_at\": \"2025-06-04T10:00:00Z\",\n      \"last_reset_time\": null,\n      \"current_time\": \"2025-06-04T14:30:00Z\",\n      \"elapsed_time_since_creation\": \"04:30:00\",\n      \"elapsed_time_since_reset\": null,\n      \"total_calculated_time\": \"04:30:00\",\n      \"assigned_devices\": 2,\n      \"accessible_areas\": 3,\n      \"status\": \"active\"\n    }\n  ]\n}"}]}, {"name": "Update Work Timer", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"work_id\": 5,\n  \"action\": \"start\",\n  \"timestamp\": \"2025-06-04T15:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/works/{{work_id}}/timer/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "{{work_id}}", "timer", ""]}, "description": "Update work timer with actions: start, pause, resume, reset"}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n  \"work_id\": 5,\n  \"action\": \"start\",\n  \"timestamp\": \"2025-06-04T15:00:00Z\"\n}"}, "url": {"raw": "{{base_url}}/lipsapi/works/5/timer/", "host": ["{{base_url}}"], "path": ["<PERSON><PERSON><PERSON>", "works", "5", "timer", ""]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"message\": \"Work timer updated successfully\",\n  \"data\": {\n    \"work_id\": 5,\n    \"action\": \"start\",\n    \"timestamp\": \"2025-06-04T15:00:00Z\",\n    \"current_status\": \"running\",\n    \"elapsed_time\": \"00:00:00\"\n  }\n}"}]}]}], "variable": [{"key": "base_url", "value": "https://your-api-domain.com", "type": "string"}, {"key": "access_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "device_id", "value": "12345", "type": "string"}, {"key": "user_id", "value": "67890", "type": "string"}, {"key": "work_id", "value": "5", "type": "string"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Add any pre-request logic here if needed"]}}]}