{"info": {"name": "Work Groups API", "description": "API collection for managing work groups", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Save Active Devices", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": 1,\n    \"workName\": \"string\",\n    \"groupNumber\": \"string\",\n    \"assignedDevices\": 0,\n    \"timeElapsed\": \"string\",\n    \"accessibleAreas\": 0\n  }\n]"}, "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", ""]}, "description": "Saves active devices information"}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": 1,\n    \"workName\": \"string\",\n    \"groupNumber\": \"string\",\n    \"assignedDevices\": 0,\n    \"timeElapsed\": \"string\",\n    \"accessibleAreas\": 0\n  }\n]"}, "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"success": true, "data": [{"id": 1, "workName": "string", "groupNumber": "string", "assignedDevices": 0, "timeElapsed": "string", "accessibleAreas": 0}]}}]}, {"name": "Get Active Devices", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", ""]}, "description": "Retrieves all active devices"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"success": true, "data": [{"id": 1, "workName": "string", "groupNumber": "string", "assignedDevices": 0, "timeElapsed": "string", "accessibleAreas": 0}]}}]}, {"name": "Update Active Device", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"workName\": \"string\",\n  \"groupNumber\": \"string\",\n  \"assignedDevices\": 0,\n  \"timeElapsed\": \"string\",\n  \"accessibleAreas\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", "1", ""]}, "description": "Updates a specific active device by ID"}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"workName\": \"string\",\n  \"groupNumber\": \"string\",\n  \"assignedDevices\": 0,\n  \"timeElapsed\": \"string\",\n  \"accessibleAreas\": 0\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/works/active-devices/1/", "host": ["{{baseUrl}}"], "path": ["api", "v1", "works", "active-devices", "1", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"success": true, "data": {"id": 1, "workName": "string", "groupNumber": "string", "assignedDevices": 0, "timeElapsed": "string", "accessibleAreas": 0}}}]}]}