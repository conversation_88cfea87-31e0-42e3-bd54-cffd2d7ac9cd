# Notification Alert
# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

---

## 🗞 Node Version

Make sure you are using **Node.js v22.14.0**.

Check your version with:

```bash
node -v
```

> If needed, install the correct version via [Node.js official site](https://nodejs.org/) or use a version manager like [nvm](https://github.com/nvm-sh/nvm).

---

## ⚙️ Setup Environment

### Using npm

```bash
npm install
npm run dev
```

### Or using yarn

```bash
yarn install
yarn run dev
```

---

## 🔌 Vite + React Plugins

Currently, two official plugins are available:

- [`@vitejs/plugin-react`](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) – uses [Babel](https://babeljs.io/) for Fast Refresh
- [`@vitejs/plugin-react-swc`](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) – uses [SWC](https://swc.rs/) for Fast Refresh

---

## 📏 Expanding the ESLint Configuration

If you're developing a **production-grade** application, consider using type-aware lint rules for better code quality.

### Example ESLint Setup (with TypeScript awareness)

```js
// eslint.config.js
export default tseslint.config({
  extends: [
    // Remove ...tseslint.configs.recommended and use:
    ...tseslint.configs.recommendedTypeChecked,
    // For stricter rules:
    ...tseslint.configs.strictTypeChecked,
    // Optional: stylistic preferences
    ...tseslint.configs.stylisticTypeChecked,
  ],
  languageOptions: {
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

---

### Add React-Specific Lint Rules

Install:

```bash
npm install eslint-plugin-react-x eslint-plugin-react-dom --save-dev
```

Then add to your config:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x';
import reactDom from 'eslint-plugin-react-dom';

export default tseslint.config({
  plugins: {
    'react-x': reactX,
    'react-dom': reactDom,
  },
  rules: {
    ...reactX.configs['recommended-typescript'].rules,
    ...reactDom.configs.recommended.rules,
  },
});
```

---

> ✅ You're now all set up with a strong development foundation using React + TypeScript + Vite.

