import MainLayout from "@/layout/MainLayout";
import ChangePassword from "@/pages/ChangePassword";
import DeviceSetting from "@/pages/DeviceSetting";
import ForgotPassword from "@/pages/ForgotPassword";
import Login from "@/pages/Login";
import Management from "@/pages/Management";
import OperationConfirm from "@/pages/OperationConfirm";
import Profile from "@/pages/Profile";
import ResetPassword from "@/pages/ResetPassword";
import UserManagement from "@/pages/UserManagement";
import { createBrowserRouter, Navigate } from "react-router";
import PrivateRoute from "./PrivateRoute";

const router = createBrowserRouter([
  {
    path: "/",
    element: <MainLayout />,
    errorElement: <div>Something went wrong</div>,
    children: [
      {
        path: "/",
        element: <Login />,
      },
      {
        path: "/login",
        element: <Login />,
      },
      {
        path: "/management",
        element: (
          <PrivateRoute roles={["admin", "user"]}>
            <Management />
          </PrivateRoute>
        ),
      },
      {
        path: "/operation-confirm",
        element: (
          <PrivateRoute roles={["admin", "user"]}>
            <OperationConfirm />
          </PrivateRoute>
        ),
      },
      {
        path: "/device-setting",
        element: (
          <PrivateRoute roles={["admin", "user"]}>
            <DeviceSetting />
          </PrivateRoute>
        ),
      },
      {
        path: "/forgot-password",
        element: <ForgotPassword />,
      },
      {
        path: "/profile",
        element: (
          <PrivateRoute roles={["admin", "user"]}>
            <Profile />
          </PrivateRoute>
        ),
      },
      {
        path: "/change-password",
        element: (
          <PrivateRoute roles={["admin", "user"]}>
            <ChangePassword />
          </PrivateRoute>
        ),
      },
      {
        path: "/user-management",
        element: (
          <PrivateRoute roles={["admin"]}>
            <UserManagement />
          </PrivateRoute>
        ),
      },
      {
        path: "/reset-password",
        element: <ResetPassword />,
      },
      // Redirect any unknown route to login
      {
        path: "*",
        element: <Navigate to="/" replace />,
      },
    ],
  },
]);

export default router;
