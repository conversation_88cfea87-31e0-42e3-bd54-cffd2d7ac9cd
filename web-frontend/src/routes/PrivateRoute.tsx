import {
  selectAccessToken,
  selectCurrentUser,
} from "@/redux/features/auth/authSlice";
import { useSelector } from "react-redux";
import { Navigate, useLocation } from "react-router";

interface PrivateRouteProps {
  children: React.ReactNode;
  roles?: string[]; // allowed roles
}

const PrivateRoute = ({ children, roles }: PrivateRouteProps) => {
  const token = useSelector(selectAccessToken);
  const user = useSelector(selectCurrentUser);
  const location = useLocation();

  if (!token) {
    // Not authenticated, redirect to login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (roles && user?.role?.name && !roles.includes(user.role.name)) {
    // Authenticated but not authorized
    return <Navigate to="/" replace />;
  }

  // Authenticated and authorized
  return <>{children}</>;
};

export default PrivateRoute;
