import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useChangePasswordMutation } from "@/redux/features/auth/authApi";
import { selectCurrentUser } from "@/redux/features/auth/authSlice";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { Link, useNavigate } from "react-router";
import { toast } from "sonner";

type FormValues = {
  password: string;
  new_password: string;
  confirmPassword: string;
};

const ChangePassword = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<FormValues>();

  const newPassword = watch("new_password", "");
  const currentUser = useSelector(selectCurrentUser);
  const [changePassword, { isLoading }] = useChangePasswordMutation();
  const navigate = useNavigate();

  const onSubmit = async (data: FormValues) => {
    try {
      if (!currentUser?.email) {
        toast.error("ユーザー情報が見つかりません。再ログインしてください。");
        return;
      }

      // Format the payload according to API requirements
      const payload = {
        email: currentUser.email,
        password: data.password,
        new_password: data.new_password,
      };

      await changePassword(payload).unwrap();
      toast.success("パスワードが正常に変更されました");
      navigate("/profile");
    } catch (err: any) {
      toast.error(err?.data?.message || "パスワード変更に失敗しました");
    }
  };

  return (
    <div className="container max-w-md mx-auto py-8 px-4">
      <Card>
        <CardHeader>
          <CardTitle>パスワード変更</CardTitle>
          <CardDescription>
            安全性を保つためにパスワードを定期的に変更してください
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">
                現在のパスワード
              </label>
              <div className="relative">
                <input
                  {...register("password", {
                    required: "現在のパスワードは必須です",
                  })}
                  type={showCurrentPassword ? "text" : "password"}
                  id="password"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background pr-10"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="new_password" className="text-sm font-medium">
                新しいパスワード
              </label>
              <div className="relative">
                <input
                  {...register("new_password", {
                    required: "新しいパスワードは必須です",
                    minLength: {
                      value: 4,
                      message: "パスワードは最低4文字必要です",
                    },
                  })}
                  type={showNewPassword ? "text" : "password"}
                  id="new_password"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background pr-10"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.new_password && (
                <p className="text-sm text-destructive">
                  {errors.new_password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium">
                新しいパスワード（確認）
              </label>
              <div className="relative">
                <input
                  {...register("confirmPassword", {
                    required: "パスワード確認は必須です",
                    validate: (value) =>
                      value === newPassword || "パスワードが一致しません",
                  })}
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background pr-10"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:text-foreground"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOffIcon className="h-4 w-4" />
                  ) : (
                    <EyeIcon className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-destructive">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            <div className="flex justify-between gap-4 pt-2">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                asChild
              >
                <Link to="/profile">キャンセル</Link>
              </Button>
              <Button type="submit" className="flex-1" disabled={isLoading}>
                {isLoading ? "変更中..." : "変更を保存"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChangePassword;
