import DeviceStatus from "@/components/custom/DeviceStatus";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  useAddActiveWorksMutation,
  useGetAllActiveWorksQuery,
} from "@/redux/features/active-work/activeWorkApi";
import {
  useAssignWorkMutation,
  useGetAllDevicesQuery,
  useGetStatusMutation,
  useRemoveWorkMutation,
} from "@/redux/features/device/deviceApi";
import {
  addWorkDetails,
  selectSelectedWork,
  selectSelectedWorks,
  selectWorkDetails,
  setSelectedWorks,
} from "@/redux/features/management/managementSlice";
import {
  useGetAllWorksQuery,
  useGetWorkByIdQuery,
  Work,
  workApi,
} from "@/redux/features/work/workApi";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";

const Management = () => {
  // RTK Query hooks
  const {
    data: devicesResponse = { data: { devices: [] } },
    isLoading: isLoadingDevices,
  } = useGetAllDevicesQuery();
  const {
    data: worksResponse = { data: { works: [] } },
    isLoading: isLoadingWorks,
  } = useGetAllWorksQuery();
  const devices = React.useMemo(
    () => devicesResponse?.data?.devices || [],
    [devicesResponse]
  );
  const [assignWork, { isLoading: isAssigning }] = useAssignWorkMutation();
  const [removeWork, { isLoading: isRemoving }] = useRemoveWorkMutation();
  const [getStatus] = useGetStatusMutation();
  const works = React.useMemo(
    () => worksResponse?.data?.works || [],
    [worksResponse]
  );
  const workOptions = React.useMemo(
    () => [{ id: "", work_name: "---" }, ...works],
    [works]
  );

  // Redux state
  const dispatch = useAppDispatch();
  const selectedWork = useAppSelector(selectSelectedWork);
  const selectedWorks = useAppSelector(selectSelectedWorks);
  const workDetails = useAppSelector(selectWorkDetails);
  const { data: workDetailsData } = useGetWorkByIdQuery(selectedWork, {
    skip: !selectedWork,
  });
  const [addActiveWorks] = useAddActiveWorksMutation();
  const { data: activeWorks } = useGetAllActiveWorksQuery(null);

  // Update the effect that adds work details
  useEffect(() => {
    if (selectedWork && workDetailsData?.success && workDetailsData?.data) {
      // Only update if the details are different
      const currentDetails = workDetails[selectedWork];
      if (
        JSON.stringify(currentDetails) !== JSON.stringify(workDetailsData.data)
      ) {
        dispatch(
          addWorkDetails({
            workId: selectedWork,
            details: workDetailsData.data,
          })
        );
      }
    }
  }, [selectedWork, workDetailsData, dispatch, workDetails]);

  // Optimize the fetchMissingWorkDetails effect
  useEffect(() => {
    const fetchMissingWorkDetails = async () => {
      // Create an array of work IDs that need fetching
      const workIdsToFetch = selectedWorks.filter(
        (workId) => workId && !workDetails[workId]
      );

      if (workIdsToFetch.length === 0) return;

      // Fetch details for each work ID using the RTK Query hooks
      for (const workId of workIdsToFetch) {
        try {
          const response = await dispatch(
            workApi.endpoints.getWorkById.initiate(workId)
          );

          const result = response.data;

          if (result?.success && result?.data) {
            dispatch(
              addWorkDetails({
                workId,
                details: result.data,
              })
            );
          }
        } catch (error) {
          console.error(`Failed to fetch details for work ${workId}:`, error);
        }
      }
    };

    fetchMissingWorkDetails();
  }, [selectedWorks, workDetails, dispatch]);

  // Optimize the updateWorkDetails effect
  useEffect(() => {
    const updateWorkDetails = async () => {
      // Only update if we have selected works
      if (selectedWorks.length === 0) return;

      for (const workId of selectedWorks) {
        try {
          const response = await dispatch(
            workApi.endpoints.getWorkById.initiate(workId)
          );

          const result = response.data;

          if (result?.success && result?.data) {
            // Only update if the details are different
            const currentDetails = workDetails[workId];
            if (
              JSON.stringify(currentDetails) !== JSON.stringify(result.data)
            ) {
              dispatch(
                addWorkDetails({
                  workId,
                  details: result.data,
                })
              );
            }
          }
        } catch (error) {
          console.error(`Failed to update details for work ${workId}:`, error);
        }
      }
    };

    updateWorkDetails();
  }, [selectedWorks, dispatch, workDetails, devices]);

  // Handle release button click - simplified
  const handleRelease = async (deviceId: string, workId: string) => {
    try {
      const payload = {
        deviceId: deviceId,
        workId: parseInt(workId, 10) || 0,
      };

      // Remove work from device
      await removeWork({
        id: deviceId,
        data: payload,
      }).unwrap();

      // Update selectedWorks if needed
      // Check if this work is still in use by other devices
      const devicesUsingThisWork = devices.filter(
        (device) => device.work_id === parseInt(workId, 10)
      );

      // If this was the last device using this work, remove from selectedWorks
      if (devicesUsingThisWork.length === 0 && selectedWorks.includes(workId)) {
        const newSelectedWorks = selectedWorks.filter((id) => id !== workId);
        dispatch(setSelectedWorks(newSelectedWorks));
        // Note: The useEffect will handle syncing to DB
      } else {
        // Work is still in use, just update work details
        await dispatch(workApi.endpoints.getWorkById.initiate(workId));
      }

      await addActiveWorks({
        work_ids: selectedWorks.map((workId) => parseInt(workId, 10)),
      }).unwrap();

      toast.success("デバイスの割り当てを解除しました");
    } catch (error) {
      toast.error("割り当て解除に失敗しました");
      console.error("Release failed:", error);
    }
  };

  // Handle work selection change - simplified
  const handleWorkChange = async (deviceId: string, newWorkId: string) => {
    try {
      const payload = {
        deviceId: deviceId,
        assignedWork: parseInt(newWorkId, 10) || 0,
      };

      await assignWork({
        id: deviceId,
        data: payload,
      }).unwrap();

      if (newWorkId) {
        // Get status with the new workId
        const workIdInt = parseInt(newWorkId, 10) || 0;
        if (workIdInt > 0) {
          await getStatus({
            deviceId: String(deviceId),
            workId: workIdInt,
          });
        }

        // Add to selectedWorks if not already included
        if (!selectedWorks.includes(newWorkId)) {
          const newSelectedWorks = [...selectedWorks, newWorkId];
          dispatch(setSelectedWorks(newSelectedWorks));
          // Note: The useEffect will handle syncing to DB
        }

        // Fetch updated work details
        await dispatch(workApi.endpoints.getWorkById.initiate(newWorkId));

        const workName =
          works.find((w) => w.id === newWorkId)?.workName || newWorkId;
        toast.success(`デバイスを ${workName} に割り当てました`);
      } else {
        toast.success("デバイスの割り当てを解除しました");
      }
    } catch (error) {
      toast.error("割り当ての更新に失敗しました");
      console.error("Assignment update failed:", error);
    }
  };
  // save the selected works to data base active works

  useEffect(() => {
    const saveSelectedWorks = async () => {
      if (selectedWorks.length > 0) {
        try {
          const response = await addActiveWorks({
            work_ids: selectedWorks.map((workId) => parseInt(workId, 10)),
          }).unwrap();

          if (response.success) {
            toast.success("作業情報を保存しました");
          } else {
            toast.error("作業情報の保存に失敗しました");
          }
        } catch (error) {
          console.error("Failed to save selected works:", error);
        }
      }
    };

    saveSelectedWorks();
  }, [selectedWorks, addActiveWorks]);

  // initially fetch active works from the database and set them to selectedWorks
  useEffect(() => {
    const fetchActiveWorks = async () => {
      if (activeWorks?.length > 0) {
        const workIds = activeWorks?.map((work: Work) => work.id);
        dispatch(setSelectedWorks(workIds));
      }
    };

    fetchActiveWorks();
  }, [activeWorks, dispatch]);

  // Update the total devices calculation
  const totalDevices = React.useMemo(() => {
    // Sum up assignedDevices from all work details
    return Object.values(workDetails).reduce((total, work) => {
      return total + (work?.assignedDevices || 0);
    }, 0);
  }, [workDetails]); // Loading state
  if (isLoadingDevices || isLoadingWorks) {
    return <div className="py-8 text-center">読み込み中...</div>;
  }

  const isUpdating = isAssigning || isRemoving;

  // Display elapsed time for workes (作業に割り当てる時間) and incidents (作業の経過時間)
  // Normalize different time formats
  function parseTimestamp(timestamp: string | null | undefined): Date | null {
    if (!timestamp) return null;
  
    try {
      // Case 1: ISO 8601 format (includes 'T')
      if (timestamp.includes('T')) {
        // Remove microseconds if present: "2025-05-29T05:09:29.447418Z" → "2025-05-29T05:09:29Z"
        const [mainPart] = timestamp.split('.');
        const cleaned = mainPart.endsWith('Z') ? mainPart : mainPart + 'Z';
        const parsedDate = new Date(cleaned);
        return isNaN(parsedDate.getTime()) ? null : parsedDate;
      }
  
      // Case 2: Space-separated format → convert to ISO-compatible
      const isoCompatible = timestamp.replace(' ', 'T');
      const parsedDate = new Date(isoCompatible);
      return isNaN(parsedDate.getTime()) ? null : parsedDate;
    } catch (error) {
      console.error('Failed to parse timestamp:', error);
      return null;
    }
  }
  
  // Calculate elapsed time (timestamp provided by back-end)
  function getElapsedTimeString(createdAt: string): string {
    const startTime = parseTimestamp(createdAt);
    if (!startTime || isNaN(startTime.getTime())) return 'Invalid date';

    const now = new Date();
    const diff = Math.floor((now.getTime() - startTime.getTime())/ 1000)

    const hours = String(Math.floor(diff / 3600)).padStart(2,'0');
    const minutes = String(Math.floor((diff % 3600) / 60)).padStart(2, '0');
    const seconds = String(diff % 60).padStart(2, '0');

    return `${hours}:${minutes}:${seconds}`;
  }

  // ⏱️ ElapsedTime Counter
  interface ElapsedTimeProps {
    createdAt: string;
  }

  function ElapsedTime({ createdAt }: ElapsedTimeProps) {
    const [elapsed, setElapsed] = useState(getElapsedTimeString(createdAt));

    useEffect(() => {
      const intervalId = setInterval(() => {
        setElapsed(getElapsedTimeString(createdAt));
      }, 1000);
  
      return () => clearInterval(intervalId); // cleanup
    }, [createdAt]);
  
    return <span>{elapsed}</span>;
  }


  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold mb-4">デバイス情報</h1>

        {/* Device Information Table with horizontal slider */}
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto w-full">
            <div className="inline-block min-w-full">
              <table className="w-full text-xs sm:text-sm whitespace-nowrap">
                <thead>
                  <tr className="bg-primary/30">
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      デバイスID
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      デバイス名
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      割当作業
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium whitespace-nowrap">
                      充電残量[%]
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      アラートステータス
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      GPS強度
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      作業時間
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      割当解除
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {devices.map((device, index) => (
                    <tr
                      key={device.id}
                      className={cn(
                        "border-t transition-colors",
                        index % 2 === 0 ? "bg-white" : "bg-blue-50"
                      )}
                    >
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.display_device_id}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.device_name}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        <select
                          value={device.work_id ? String(device.work_id) : ""}
                          onChange={(e) => {
                            handleWorkChange(
                              device.display_device_id,
                              e.target.value
                            );
                          }}
                          disabled={isUpdating}
                          className="w-full border rounded-md px-1 sm:px-2 py-1 text-xs sm:text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
                        >
                          {workOptions.map((work) => (
                            <option key={work.id || "empty"} value={work.id}>
                              {"work_name" in work ? work.work_name : "未選択"}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.battery}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {<DeviceStatus
                            deviceId={device.display_device_id}
                            workId={device.work_id}
                            defaultStatus={device.status}/>
                        }
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        [...]
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.work_id ? <ElapsedTime createdAt={device.assignedAt} /> : "---"}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.work_id && (
                          <Button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRelease(
                                device.display_device_id,
                                String(device.work_id)
                              );
                            }}
                            variant="outline"
                            size="sm"
                            disabled={isUpdating}
                            className="px-2 py-1 h-auto text-xs sm:text-sm"
                          >
                            許可
                          </Button>
                        )}
                        {device.work_id ? <Button
                          variant="outline"
                          size="sm"
                          disabled={isUpdating}
                          className="px-2 py-1 h-auto text-xs sm:text-sm"
                          >
                            不許可
                          </Button> : ""
                        }
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Separator */}
      <hr className="border-t-4 my-4 border-gray-600" />

      {/* Operations Information */}
      <div>
        {" "}
        <div className="flex items-center justify-between">
          <h1 className="text-xl sm:text-2xl font-bold mb-4">作業情報</h1>{" "}
          <div>
            <h1 className="text-xl sm:text-2xl font-bold mb-4 inline-block">
              デバイス合計数: {totalDevices || 0}
            </h1>
          </div>
        </div>
        {/* Show table if any works are selected and have details */}
        {selectedWorks.length > 0 ? (
          <div className="overflow-x-auto rounded-lg border">
            <div className="inline-block min-w-full">
              <table className="w-full text-xs sm:text-sm whitespace-nowrap">
                <thead>
                  <tr className="bg-primary/30">
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      作業名
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium whitespace-nowrap">
                      グループ番号
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium whitespace-nowrap">
                      割当デバイス数
                    </th>
                    <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                      経過時間
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {selectedWorks.map((workId) => {
                    const details = workDetails[workId];
                    // Show the work even if details are not available yet
                    return (
                      <tr key={workId} className="bg-white border-t">
                        <td className="py-2 sm:py-3 px-2 sm:px-4">
                          {details?.workName || "---"}
                        </td>
                        <td className="py-2 sm:py-3 px-2 sm:px-4">
                          {details?.groupNumber || "---"}
                        </td>
                        <td className="py-2 sm:py-3 px-2 sm:px-4">
                          {details?.assignedDevices ?? 0}
                        </td>
                        <td className="py-2 sm:py-3 px-2 sm:px-4">
                          {details?.createdAt ? (<ElapsedTime createdAt={details?.createdAt} />
                          ) : ( "---"
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 sm:py-10 bg-muted/20 rounded-lg border">
            <p className="text-muted-foreground text-sm">
              デバイス情報テーブルから作業を選択してください
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Management;
