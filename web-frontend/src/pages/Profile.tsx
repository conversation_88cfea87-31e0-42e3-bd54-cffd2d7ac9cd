import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { selectCurrentUser } from "@/redux/features/auth/authSlice";
import { useAppSelector } from "@/redux/hooks";
import { User } from "lucide-react";
import moment from "moment";
import { Link } from "react-router";

// Mock user data (will be replaced with Redux state in real implementation)

const Profile = () => {
  const user = useAppSelector(selectCurrentUser);

  return (
    <div className="container max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">プロフィール</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Profile Info Card */}
        <Card className="col-span-1 md:col-span-1 h-min">
          <CardHeader className="pb-3">
            <CardTitle className="text-center">アカウント</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center">
            <div className="bg-primary/10 rounded-full p-6 mb-4">
              <User className="h-12 w-12 text-primary" />
            </div>
            <h2 className="text-xl font-semibold">{user?.full_name}</h2>
            <p className="text-sm text-muted-foreground mb-4">
              {user?.role.name}
            </p>
            <Button
              variant="outline"
              className="w-full mb-2 bg-primary/10 text-primary hover:bg-primary/20"
              asChild
            >
              <Link to="/change-password">パスワード変更</Link>
            </Button>
          </CardContent>
        </Card>

        {/* Profile Details Card */}
        <Card className="col-span-1 md:col-span-2 h-min">
          <CardHeader className="pb-3">
            <CardTitle>プロフィール詳細</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-1">
                <p className="text-sm text-muted-foreground">名前</p>
                <p className="font-medium">{user?.full_name}</p>
              </div>
              <div className="grid grid-cols-1 gap-1">
                <p className="text-sm text-muted-foreground">メールアドレス</p>
                <p className="font-medium">{user?.email}</p>
              </div>
              <div className="grid grid-cols-1 gap-1">
                <p className="text-sm text-muted-foreground">役割</p>
                <p className="font-medium">{user?.role?.name}</p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <p className="text-sm text-muted-foreground">最終ログイン</p>
                <p className="font-medium">
                  {user?.last_login
                    ? moment(user.last_login).format("YYYY-MM-DD HH:mm")
                    : "-"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Profile;
