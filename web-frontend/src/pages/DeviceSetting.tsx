import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { selectCurrentUser } from "@/redux/features/auth/authSlice";
import {
  type DeviceSetting as DeviceSettingType,
  useGetAllDeviceSettingsQuery,
  useUpdateDeviceSettingMutation,
} from "@/redux/features/device-setting/deviceSettingApi";
import { useAppSelector } from "@/redux/hooks";
import { Settings } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

// Type for form values during editing
interface EditValues {
  name?: string;
  approachDistance: number;
  approachSeconds: number;
  assignedAt?: string;
}

const DeviceSetting = () => {
  // RTK Query hooks
  const { data, isLoading, isError, refetch } = useGetAllDeviceSettingsQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
    }
  );

  const deviceSettings = data?.data.deviceSettings || [];

  const [updateDeviceSetting, { isLoading: isUpdating }] =
    useUpdateDeviceSettingMutation();

  // Local state for editing
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [isEditingMode, setIsEditingMode] = useState(false);
  const [editValues, setEditValues] = useState<EditValues | null>(null);
  // User info from Redux
  const user = useAppSelector(selectCurrentUser);
  const isAdmin = user?.role?.name === "admin";
  const canEdit = isAdmin;

  // Start editing a device row
  const handleEditStart = (device: DeviceSettingType) => {
    if (!canEdit) {
      toast.error("管理者のみが設定を変更できます");
      return;
    }
    setSelectedDeviceId(device.id.toString());
    setIsEditingMode(true);
    setEditValues({
      name: device.name,
      approachDistance: device.approachDistance,
      approachSeconds: device.approachSeconds,
      assignedAt: device.assignedAt,
    });
  };

  // Handle form input changes
  const handleInputChange = (field: string, value: string | number) => {
    if (!canEdit) return;
    if (editValues) {
      setEditValues({
        ...editValues,
        [field]: value,
      });
    }
  };
  const handleSaveChanges = async () => {
    if (!canEdit) {
      toast.error("管理者のみが設定を変更できます");
      return;
    }
    if (selectedDeviceId && editValues) {
      try {
        const updateData = {
          name: editValues.name,
          approachDistance: editValues.approachDistance,
          approachSeconds: editValues.approachSeconds,
          assignedAt: editValues.assignedAt,
        };

        await updateDeviceSetting({
          id: selectedDeviceId,
          data: updateData,
        }).unwrap();
        toast.success("デバイス設定が更新されました");

        // Manually refetch the data after a successful update
        refetch();
      } catch (error) {
        toast.error("デバイス設定の更新に失敗しました");
        console.error("Update failed:", error);
      } finally {
        // Always reset editing state
        setIsEditingMode(false);
        setSelectedDeviceId(null);
        setEditValues(null);
      }
    }
  };

  if (isLoading) {
    return <div className="py-8 text-center">読み込み中...</div>;
  }

  if (isError) {
    return (
      <div className="py-8 text-center text-red-500">
        データの取得中にエラーが発生しました。
        <Button variant="outline" className="ml-2" onClick={() => refetch()}>
          再試行
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-xl sm:text-2xl font-bold mb-4">デバイス設定</h1>

        {/* Device Settings Table */}
        <div className="overflow-x-auto rounded-lg border">
          <table className="w-full text-xs sm:text-sm">
            <thead>
              <tr className="bg-primary/30">
                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                  名称
                </th>
                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                  デバイスID
                </th>
                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium whitespace-nowrap">
                  接近エリア距離(m)
                </th>
                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium whitespace-nowrap">
                  接近エリア秒数
                </th>
                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                  状態
                </th>

                <th className="py-2 sm:py-3 px-2 sm:px-4 text-left font-medium">
                  設定
                </th>
              </tr>
            </thead>
            <tbody>
              {deviceSettings.map(
                (device: DeviceSettingType, index: number) => {
                  const isEditing =
                    device.id.toString() === selectedDeviceId && isEditingMode;
                  return (
                    <tr
                      key={device.id}
                      className={cn(
                        "border-t transition-colors",
                        index % 2 === 0 ? "bg-white" : "bg-blue-50",
                        isEditing && "bg-blue-100"
                      )}
                    >
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {isEditing && isAdmin ? (
                          <Input
                            value={editValues?.name || ""}
                            onChange={(e) =>
                              handleInputChange("name", e.target.value)
                            }
                            className="h-7 sm:h-8 px-1 sm:px-2 text-xs sm:text-sm"
                          />
                        ) : (
                          device.name || "-"
                        )}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {device.deviceId || "-"}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {isEditing && canEdit ? (
                          <Input
                            type="number"
                            value={editValues?.approachDistance}
                            onChange={(e) =>
                              handleInputChange(
                                "approachDistance",
                                parseFloat(e.target.value)
                              )
                            }
                            className="h-7 sm:h-8 px-1 sm:px-2 text-xs sm:text-sm"
                            step="0.1"
                            min="0"
                          />
                        ) : device.approachDistance !== undefined ? (
                          device.approachDistance.toFixed(1)
                        ) : (
                          "-"
                        )}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {isEditing && canEdit ? (
                          <Input
                            type="number"
                            value={editValues?.approachSeconds}
                            onChange={(e) =>
                              handleInputChange(
                                "approachSeconds",
                                parseInt(e.target.value)
                              )
                            }
                            className="h-7 sm:h-8 px-1 sm:px-2 text-xs sm:text-sm"
                            min="0"
                          />
                        ) : device.approachSeconds !== undefined ? (
                          device.approachSeconds
                        ) : (
                          "-"
                        )}
                      </td>
                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        <div className="flex items-center">
                          <span
                            className={cn(
                              "inline-block w-2 sm:w-3 h-2 sm:h-3 rounded-full mr-1 sm:mr-2",
                              device.status === "normal"
                                ? "bg-gray-400"
                                : device.status === "waiting"
                                ? "bg-orange-500"
                                : device.status === "areaChange"
                                ? "bg-blue-200"
                                : device.status === "unstableLocation"
                                ? "bg-purple-500"
                                : device.status === "prohibitedAreaApproach"
                                ? "bg-yellow-500"
                                : device.status === "prohibitedAreaEnter"
                                ? "bg-red-500"
                                : "bg-gray-400"
                            )}
                          />
                          <span className="whitespace-nowrap">
                            {device.status === "normal"
                              ? "アラート無し"
                              : device.status === "waiting"
                              ? "サーバー応答待ち"
                              : device.status === "areaChange"
                              ? "エリア変更"
                              : device.status === "unstableLocation"
                              ? "位置情報不安定"
                              : device.status === "prohibitedAreaApproach"
                              ? "進入禁止エリア接近"
                              : device.status === "prohibitedAreaEnter"
                              ? "進入禁止エリア進入"
                              : "不明"}
                          </span>
                        </div>
                      </td>

                      <td className="py-2 sm:py-3 px-2 sm:px-4">
                        {isEditing ? (
                          <Button
                            size="sm"
                            onClick={handleSaveChanges}
                            disabled={isUpdating}
                            className="bg-green-500 hover:bg-green-700 px-2 py-1 h-auto text-xs sm:text-sm"
                          >
                            {isUpdating ? "保存中..." : "保存"}
                          </Button>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditStart(device)}
                            className="p-1 h-auto cursor-pointer"
                            title={
                              isAdmin ? "設定を変更" : "距離と秒数のみ変更可能"
                            }
                          >
                            <Settings className="h-3 w-3 sm:h-4 sm:w-4" />
                          </Button>
                        )}
                      </td>
                    </tr>
                  );
                }
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DeviceSetting;
