import { Button } from "@/components/ui/button";
import { selectCurrentUser } from "@/redux/features/auth/authSlice";
import { useAppSelector } from "@/redux/hooks";
import { Link } from "react-router";

const Home = () => {
  const user = useAppSelector(selectCurrentUser);

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-5rem)] py-8">
      <div className="w-full max-w-lg p-8 bg-card rounded-xl shadow-lg border border-border text-center">
        <h1 className="text-3xl font-bold text-primary mb-4">
          通知アラートへようこそ
        </h1>
        {user ? (
          <>
            <p className="text-lg font-medium text-foreground mb-2">
              こんにちは、
              <span className="text-primary font-bold">
                {user.full_name}
              </span>{" "}
            </p>
            <p className="text-muted-foreground text-sm mb-4">
              ご利用いただきありがとうございます。
            </p>
          </>
        ) : (
          <>
            <p className="text-lg text-foreground mb-4">
              デバイス管理・作業管理・通知アラートを簡単に操作できます。
            </p>
            <div className="flex flex-col gap-3 mt-6">
              <Button asChild variant="default">
                <Link to="/login">ログイン</Link>
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Home;
