import { Button } from "@/components/ui/button";
import { useForgotPasswordMutation } from "@/redux/features/auth/authApi";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router";
import { toast } from "sonner";

type FormValues = {
  email: string;
};

const ForgotPassword = () => {
  const [forgotPassword, { isLoading: isSending }] =
    useForgotPasswordMutation();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>();

  const onSubmitEmail = async (data: Pick<FormValues, "email">) => {
    try {
      const res = await forgotPassword(data).unwrap();
      if (res.status) {
        toast.success("リセットリンクを送信しました。メールをご確認ください。");

        navigate("/reset-password");
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      toast.error(err?.data?.message || "リセットリンク送信に失敗しました");
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-5rem)]">
      <div className="w-full max-w-md p-8 space-y-6 bg-card rounded-xl shadow-lg border border-border">
        <div className="text-center">
          <h1 className="text-2xl font-bold tracking-tight text-foreground">
            パスワードをお忘れの方
          </h1>
          <p className="text-sm text-muted-foreground mt-2">
            パスワードリセットリンクを受け取るにはメールアドレスを入力してください
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmitEmail)} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              メールアドレス
            </label>
            <input
              {...register("email", {
                required: "メールアドレスは必須です",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "無効なメールアドレスです",
                },
              })}
              type="email"
              id="email"
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background"
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>
          <Button
            type="submit"
            className="w-full"
            variant="default"
            disabled={isSending}
          >
            {isSending ? "送信中..." : "リセットリンクを送信"}
          </Button>
          <div className="text-center text-sm">
            <Link to="/login" className="text-primary hover:underline">
              ログインに戻る
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ForgotPassword;
