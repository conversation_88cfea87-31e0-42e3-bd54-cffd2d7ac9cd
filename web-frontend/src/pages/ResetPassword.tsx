import { Button } from "@/components/ui/button";
import { useResetPasswordMutation } from "@/redux/features/auth/authApi";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router";
import { toast } from "sonner";

type FormValues = {
  email: string;
  otp: string;
  password: string;
  confirmPassword: string;
};

const ResetPassword = () => {
  const [resetSuccess, setResetSuccess] = useState(false);
  const [resetPassword, { isLoading }] = useResetPasswordMutation();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<FormValues>();

  const password = watch("password", "");

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const onSubmit = async (data: FormValues) => {
    if (data.password !== data.confirmPassword) {
      toast.error("パスワードが一致しません");
      return;
    }

    const resetData = {
      email: data.email,
      otp: data.otp,
      password: data.password,
    };

    try {
      await resetPassword(resetData).unwrap();
      toast.success("パスワードが正常にリセットされました");
      setResetSuccess(true);
      setTimeout(() => {
        navigate("/login");
      }, 2000);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      toast.error(err?.data?.message || "パスワードリセットに失敗しました");
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-5rem)]">
      <div className="w-full max-w-md p-8 space-y-6 bg-card rounded-xl shadow-lg border border-border">
        <div className="text-center">
          <h1 className="text-2xl font-bold tracking-tight text-foreground">
            パスワードリセット
          </h1>
          <p className="text-sm text-muted-foreground mt-2">
            メールで受け取ったOTPと新しいパスワードを入力してください
          </p>
        </div>

        {resetSuccess ? (
          <div className="text-center space-y-4 py-8">
            <div className="text-green-600 text-lg font-semibold">
              パスワードがリセットされました
            </div>
            <div className="text-muted-foreground text-sm">
              新しいパスワードでログインできるようになりました。
            </div>
            <div className="text-sm text-muted-foreground">
              ログインページにリダイレクトします...
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                メールアドレス
              </label>
              <input
                {...register("email", {
                  required: "メールアドレスは必須です",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "有効なメールアドレスを入力してください",
                  },
                })}
                type="email"
                id="email"
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background"
                placeholder="メールアドレスを入力"
              />
              {errors.email && (
                <p className="text-sm text-destructive">
                  {errors.email.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="otp" className="text-sm font-medium">
                ワンタイムパスワード (OTP)
              </label>
              <input
                {...register("otp", {
                  required: "OTPは必須です",
                  minLength: {
                    value: 6,
                    message: "OTPは6桁以上必要です",
                  },
                })}
                type="text"
                id="otp"
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background"
                placeholder="メールで受け取ったOTPを入力"
              />
              {errors.otp && (
                <p className="text-sm text-destructive">{errors.otp.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">
                新しいパスワード
              </label>
              <div className="relative">
                <input
                  {...register("password", {
                    required: "パスワードは必須です",
                    minLength: {
                      value: 8,
                      message: "パスワードは8文字以上必要です",
                    },
                  })}
                  type={showPassword ? "text" : "password"}
                  id="password"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background pr-10"
                  placeholder="新しいパスワード"
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400"
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium">
                パスワード確認
              </label>
              <div className="relative">
                <input
                  {...register("confirmPassword", {
                    required: "パスワード確認は必須です",
                    validate: (value) =>
                      value === password || "パスワードが一致しません",
                  })}
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary bg-background pr-10"
                  placeholder="新しいパスワードを再入力"
                />
                <button
                  type="button"
                  onClick={toggleConfirmPasswordVisibility}
                  className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400"
                >
                  {showConfirmPassword ? (
                    <EyeOff size={18} />
                  ) : (
                    <Eye size={18} />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-destructive">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              variant="default"
              disabled={isLoading}
            >
              {isLoading ? "処理中..." : "パスワードをリセット"}
            </Button>

            <div className="text-center text-sm">
              <Link to="/login" className="text-primary hover:underline">
                ログインに戻る
              </Link>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ResetPassword;
