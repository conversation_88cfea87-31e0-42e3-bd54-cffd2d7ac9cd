import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { User } from "../redux/features/auth/authApi";
import { selectCurrentUser } from "../redux/features/auth/authSlice";
import { useGetAllDevicesQuery } from "../redux/features/device/deviceApi";
import { useUpdateUserByIdMutation } from "../redux/features/user-management/userManagement";
import { useAppSelector } from "../redux/hooks";

interface UpdateUserModalProps {
  visible: boolean;
  onClose: () => void;
  user: User;
}

const UpdateUserModal: React.FC<UpdateUserModalProps> = ({
  visible,
  onClose,
  user,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      full_name: "",
      username: "",
      email: "",
      phone_number: "",
      role: "",
      company_name: "",
      notes: "",
      device_id: "",
    },
  });

  const [updateUser, { isLoading }] = useUpdateUserByIdMutation();
  const { data: devicesResponse, isLoading: isLoadingDevices } =
    useGetAllDevicesQuery();
  const currentUser = useAppSelector(selectCurrentUser);
  const isCurrentUser = currentUser?.id === user?.id;

  const devices = devicesResponse?.data?.devices || [];

  useEffect(() => {
    if (visible && user) {
      // Set form values
      reset({
        full_name: user.full_name,
        username: user.username,
        email: user.email,
        phone_number: user.phone_number,
        company_name: user.company_name,
        notes: user.notes,
        role: typeof user.role === "object" ? user.role.name : user.role,
        device_id: user.device_id || "",
      });
    }
  }, [visible, user, reset]);
  const onSubmit = async (data: {
    full_name: string;
    username: string;
    email: string;
    phone_number: string;
    role: string;
    company_name: string;
    notes: string;
    device_id: string;
  }) => {
    try {
      // Add special handling for role if needed
      const payload = {
        id: user.id,
        ...data,
        role_name: data.role,
      };

      const res = await updateUser(payload).unwrap();

      if (res.status) toast.success("ユーザーの更新に成功しました");
      onClose();
    } catch (error) {
      console.error("ユーザーの更新に失敗しました：", error);
      toast.error(" ユーザーの更新に失敗しました");
    }
  };

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="max-w-md sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl">ユーザー編集</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="full_name">氏名</Label>
              <Input
                id="full_name"
                placeholder="氏名を入力してください"
                {...register("full_name", {
                  required: "氏名は必須項目です",
                })}
              />
              {errors.full_name && (
                <p className="text-sm text-destructive">
                  {errors.full_name.message}
                </p>
              )}
            </div>
            {/* Username */}
            <div className="space-y-2">
              <Label htmlFor="username">ユーザー名</Label>
              <Input
                id="username"
                placeholder="ユーザー名を入力してください"
                {...register("username", {
                  required: "ユーザー名は必須項目です",
                })}
              />
              {errors.username && (
                <p className="text-sm text-destructive">
                  {errors.username.message}
                </p>
              )}
            </div>
            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">メールアドレス</Label>
              <Input
                id="email"
                placeholder="メールアドレスを入力してください"
                type="email"
                {...register("email", {
                  required: "メールアドレスは必須項目です",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "無効なメールアドレスです",
                  },
                })}
              />
              {errors.email && (
                <p className="text-sm text-destructive">
                  {errors.email.message}
                </p>
              )}
            </div>
            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phone_number">電話番号</Label>
              <Input
                id="phone_number"
                placeholder="電話番号を入力してください"
                {...register("phone_number", {
                  required: "電話番号は必須項目です",
                })}
              />
              {errors.phone_number && (
                <p className="text-sm text-destructive">
                  {errors.phone_number.message}
                </p>
              )}
            </div>{" "}
            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role">
                役割
                {isCurrentUser && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    (自分自身のロールは変更できません)
                  </span>
                )}
              </Label>
              <select
                id="role"
                className={cn(
                  "w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none",
                  isCurrentUser && "opacity-60 cursor-not-allowed"
                )}
                {...register("role", { required: "役割は必須項目です" })}
                disabled={isCurrentUser}
              >
                <option value="">役割を選択してください</option>
                <option value="admin">管理者</option>
                <option value="user">ユーザー</option>
              </select>
              {errors.role && (
                <p className="text-sm text-destructive">
                  {errors.role.message}
                </p>
              )}
            </div>
            {/* Device Assignment */}
            <div className="space-y-2">
              <Label htmlFor="device_id">
                デバイス割り当て
                <span className="ml-1 text-xs text-muted-foreground">
                  (任意)
                </span>
              </Label>
              <select
                id="device_id"
                className={cn(
                  "w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none"
                )}
                {...register("device_id")}
                disabled={isLoadingDevices}
              >
                <option value="">デバイスを選択してください</option>
                {devices.map((device) => (
                  <option key={device.id} value={device.id}>
                    {device.display_device_id} - {device.device_name}
                  </option>
                ))}
              </select>
              {isLoadingDevices && (
                <p className="text-xs text-muted-foreground">
                  デバイス読み込み中...
                </p>
              )}
            </div>
            {/* Company Name - Simplified */}
            <div className="space-y-2">
              <Label htmlFor="company_name">会社名</Label>
              <Input
                id="company_name"
                placeholder="会社名を入力してください"
                {...register("company_name", {
                  required: "会社名は必須項目です",
                })}
              />
              {errors.company_name && (
                <p className="text-sm text-destructive">
                  {errors.company_name.message}
                </p>
              )}
            </div>
            {/* notes */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="notes">ノート</Label>
              <textarea
                id="notes"
                className={cn(
                  "w-full min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none"
                )}
                placeholder="ノートを入力してください。"
                {...register("notes", { required: "ノートは必要です。" })}
              />
              {errors.notes && (
                <p className="text-sm text-destructive">
                  {errors.notes.message}
                </p>
              )}
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              キャンセル
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "更新中..." : "ユーザーを更新"}
            </Button>{" "}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateUserModal;
