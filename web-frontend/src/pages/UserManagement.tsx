/* eslint-disable @typescript-eslint/no-explicit-any */
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Edit, Plus, Trash2, User as UserIcon } from "lucide-react";
import React, { useState } from "react";
import { toast } from "sonner";
import { User } from "../redux/features/auth/authApi";
import { selectCurrentUser } from "../redux/features/auth/authSlice";
import { useGetAllDevicesQuery } from "../redux/features/device/deviceApi";
import {
  useDeleteUserMutation,
  useGetAllUsersQuery,
} from "../redux/features/user-management/userManagement.ts";
import { useAppSelector } from "../redux/hooks";
import AddUserModal from "./AddUserModal.tsx";
import UpdateUserModal from "./UpdateUserModal.tsx";

const UserManagement: React.FC = () => {
  const { data: usersData, isFetching, isLoading } = useGetAllUsersQuery(null);
  const { data: devicesResponse } = useGetAllDevicesQuery();
  const [deleteUser, { isLoading: isDeleting }] = useDeleteUserMutation();
  const currentUser = useAppSelector(selectCurrentUser);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isUpdateModalVisible, setIsUpdateModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<number | null>(null);

  const devices = devicesResponse?.data?.devices || [];

  if (isFetching || isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        読み込み中....
      </div>
    );
  }

  const users =
    usersData?.data?.map((user: any) => ({
      ...user,
      company_name:
        typeof user.company_name === "object"
          ? user.company_name?.id || user.company_name?.name || "N/A"
          : user.company_name,
      isCurrentUser: user.id === currentUser?.id,
    })) || [];

  const handleDeleteClick = (id: number) => {
    setUserToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    try {
      await deleteUser(userToDelete).unwrap();
      toast.success("ユーザーが正常に削除されました");
      setIsDeleteDialogOpen(false);
    } catch (error) {
      toast.error("ユーザーの削除に失敗しました");
      console.log(error);
    }
  };
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setIsUpdateModalVisible(true);
  };

  const getDeviceInfo = (userId: number) => {
    const device = devices.find((d) => d.user_id === userId);
    return device
      ? `${device.display_device_id} - ${device.device_name}`
      : "0020";
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red-100 text-red-800 border-red-200";
      case "user":
        return "bg-green-100 text-green-800 border-green-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h1 className="text-2xl font-bold">ユーザー管理</h1>
        <Button
          onClick={() => setIsAddModalVisible(true)}
          disabled={currentUser?.role?.name !== "admin"}
          className={cn(
            currentUser?.role?.name !== "admin" &&
              "opacity-50 cursor-not-allowed"
          )}
          title={
            currentUser?.role?.name !== "admin"
              ? "ユーザーを追加できるのは管理者のみです"
              : "ユーザー追加"
          }
        >
          <Plus className="mr-2 h-4 w-4" /> ユーザー追加
        </Button>
      </div>

      <Card className="shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">ユーザー一覧 </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full divide-y divide-border">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    名前
                  </th>
                  {/* <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    ユーザー名
                  </th> */}
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    メールアドレス
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    役割
                  </th>{" "}
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    電話番号
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    会社
                  </th>{" "}
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    デバイスID
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    ノート
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {users.map((user: any) => (
                  <tr
                    key={user.id}
                    className={cn(
                      "hover:bg-muted/50 transition-colors",
                      user.id === currentUser?.id && "bg-blue-50"
                    )}
                  >
                    <td className="px-4 py-3 text-sm">
                      <div className="flex items-center">
                        <div className="mr-2 bg-primary text-primary-foreground h-7 w-7 rounded-full flex items-center justify-center">
                          <UserIcon className="h-4 w-4" />
                        </div>
                        <div>
                          {user.full_name}
                          {user.id === currentUser?.id && (
                            <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                              あなた
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    {/* <td className="px-4 py-3 text-sm">{user.username}</td> */}
                    <td className="px-4 py-3 text-sm">{user.email}</td>
                    <td className="px-4 py-3 text-sm">
                      <span
                        className={cn(
                          "px-2 py-1 rounded-full text-xs font-medium",
                          getRoleColor(user.role?.name)
                        )}
                      >
                        {user.role?.name}
                      </span>
                    </td>{" "}
                    <td className="px-4 py-3 text-sm">{user.phone_number}</td>
                    <td className="px-4 py-3 text-sm">{user.company_name}</td>
                    <td className="px-4 py-3 text-sm">
                      <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                        {getDeviceInfo(user.id)}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">{user.notes}</td>
                    <td className="px-4 py-3 text-sm">
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(user)}
                          disabled={currentUser?.role?.name !== "admin"}
                          title={
                            currentUser?.role?.name !== "admin"
                              ? "Only Admin can edit users"
                              : "Edit User"
                          }
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-4 w-4 text-blue-500" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteClick(user.id)}
                          disabled={
                            user.id === currentUser?.id ||
                            currentUser?.role?.name !== "admin"
                          }
                          title={
                            user.id === currentUser?.id
                              ? "You cannot delete your own account"
                              : currentUser?.role?.name !== "admin"
                              ? "Only Admin can delete users"
                              : "Delete User"
                          }
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>削除の確認</DialogTitle>
            <DialogDescription>
              このユーザーを本当に削除しますか？この操作は元に戻せません。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              キャンセル
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "削除中..." : "削除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add User Modal */}
      <AddUserModal
        visible={isAddModalVisible}
        onClose={() => setIsAddModalVisible(false)}
      />

      {/* Update User Modal */}
      {selectedUser && (
        <UpdateUserModal
          visible={isUpdateModalVisible}
          onClose={() => setIsUpdateModalVisible(false)}
          user={selectedUser}
        />
      )}
    </div>
  );
};

export default UserManagement;
