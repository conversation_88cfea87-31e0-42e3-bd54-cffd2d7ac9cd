import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>itle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useGetAllDevicesQuery } from "../redux/features/device/deviceApi";
import { useAddUserMutation } from "../redux/features/user-management/userManagement";

interface AddUserModalProps {
  visible: boolean;
  onClose: () => void;
}

interface UserFormData {
  full_name: string;
  username: string;
  email: string;
  password: string;
  phone_number: string;
  role: string;
  company_name: string;
  notes: string;
  device_id: string;
}

const AddUserModal: React.FC<AddUserModalProps> = ({ visible, onClose }) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UserFormData>({
    defaultValues: {
      full_name: "",
      username: "",
      email: "",
      password: "",
      phone_number: "",
      role: "",
      company_name: "",
      notes: "",
      device_id: "",
    },
  });

  const [addUser, { isLoading }] = useAddUserMutation();
  const { data: devicesResponse, isLoading: isLoadingDevices } =
    useGetAllDevicesQuery();

  const devices = devicesResponse?.data?.devices || [];

  const onSubmit = async (data: UserFormData) => {
    try {
      // Convert role string to number if needed
      if (data.role) {
        data.role = parseInt(data.role) as any;
      }

      await addUser(data).unwrap();
      toast.success("ユーザーが正常に追加されました");
      reset();
      onClose();
    } catch (error) {
      console.error("ユーザーの追加に失敗しました:", error);
      toast.error("ユーザーの追加に失敗しました");
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="max-w-md sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl">新しいユーザーを追加</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Full Name */}
            <div className="space-y-2">
              <Label htmlFor="full_name">氏名</Label>
              <Input
                id="full_name"
                placeholder="氏名を入力してください"
                {...register("full_name", {
                  required: "氏名は必須項目です",
                })}
              />
              {errors.full_name && (
                <p className="text-sm text-destructive">
                  {errors.full_name.message}
                </p>
              )}
            </div>
            {/* Username */}
            <div className="space-y-2">
              <Label htmlFor="username">ユーザー名</Label>
              <Input
                id="username"
                placeholder="ユーザー名を入力してください"
                {...register("username", {
                  required: "ユーザー名は必須項目です",
                })}
              />
              {errors.username && (
                <p className="text-sm text-destructive">
                  {errors.username.message}
                </p>
              )}
            </div>
            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">メールアドレス</Label>
              <Input
                id="email"
                placeholder="メールアドレスを入力してください"
                type="email"
                {...register("email", {
                  required: "メールアドレスは必須項目です",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "無効なメールアドレスです",
                  },
                })}
              />
              {errors.email && (
                <p className="text-sm text-destructive">
                  {errors.email.message}
                </p>
              )}
            </div>
            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">パスワード</Label>
              <Input
                id="password"
                placeholder="パスワードを入力してください"
                type="password"
                {...register("password", {
                  required: "パスワードは必須項目です",
                  minLength: {
                    value: 8,
                    message: "パスワードは8文字以上である必要があります",
                  },
                })}
              />
              {errors.password && (
                <p className="text-sm text-destructive">
                  {errors.password.message}
                </p>
              )}
            </div>
            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phone_number">電話番号</Label>
              <Input
                id="phone_number"
                placeholder="電話番号を入力してください"
                {...register("phone_number", {
                  required: "電話番号は必須項目です",
                })}
              />
              {errors.phone_number && (
                <p className="text-sm text-destructive">
                  {errors.phone_number.message}
                </p>
              )}
            </div>{" "}
            {/* Role */}
            <div className="space-y-2">
              <Label htmlFor="role">役割</Label>
              <select
                id="role"
                className={cn(
                  "w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none"
                )}
                {...register("role", { required: "役割は必須項目です" })}
              >
                <option value="">役割を選択してください</option>
                <option value="1">管理者</option>
                <option value="3">ユーザー</option>
              </select>
              {errors.role && (
                <p className="text-sm text-destructive">
                  {errors.role.message}
                </p>
              )}
            </div>
            {/* Device Assignment */}
            <div className="space-y-2">
              <Label htmlFor="device_id">
                デバイス割り当て
                <span className="ml-1 text-xs text-muted-foreground">
                  (任意)
                </span>
              </Label>
              <select
                id="device_id"
                className={cn(
                  "w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none"
                )}
                {...register("device_id")}
                disabled={isLoadingDevices}
              >
                <option value="">デバイスを選択してください</option>
                {devices.map((device) => (
                  <option key={device.id} value={device.id}>
                    {device.display_device_id} - {device.device_name}
                  </option>
                ))}
              </select>
              {isLoadingDevices && (
                <p className="text-xs text-muted-foreground">
                  デバイス読み込み中...
                </p>
              )}
            </div>
            {/* Company Name - Simplified */}
            <div className="space-y-2">
              <Label htmlFor="company_name">会社名</Label>
              <Input
                id="company_name"
                placeholder="会社名を入力してください"
                {...register("company_name", {
                  required: "会社名は必須項目です",
                })}
              />
              {errors.company_name && (
                <p className="text-sm text-destructive">
                  {errors.company_name.message}
                </p>
              )}
            </div>
            {/* notes */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="notes">ノート</Label>
              <textarea
                id="notes"
                className={cn(
                  "w-full min-h-[80px] rounded-md border border-input bg-background px-3 py-2 text-sm shadow-xs",
                  "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-none"
                )}
                placeholder="ノートを入力してください。"
                {...register("notes", { required: "ノートは必要です。" })}
              />
              {errors.notes && (
                <p className="text-sm text-destructive">
                  {errors.notes.message}
                </p>
              )}
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={handleCancel}>
              キャンセル
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "作成中...." : "ユーザーを作成"}
            </Button>{" "}
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddUserModal;
