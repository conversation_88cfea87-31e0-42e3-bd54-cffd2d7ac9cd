import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { logout, selectCurrentUser } from "@/redux/features/auth/authSlice";
import { LogOut, Menu, Settings, User, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { Link, useLocation } from "react-router";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const location = useLocation();
  const userMenuRef = useRef<HTMLDivElement>(null);
  const user = useAppSelector(selectCurrentUser);
  const dispatch = useAppDispatch();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setIsUserMenuOpen(false);
  }, [location.pathname]);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  const userItems = [
    { name: "管理", path: "/management" },
    { name: "操作確認", path: "/operation-confirm" },
    { name: "デバイス設定", path: "/device-setting" },
  ];
  const adminItems = [
    { name: "管理", path: "/management" },
    { name: "操作確認", path: "/operation-confirm" },
    { name: "デバイス設定", path: "/device-setting" },
  ];

  let roleItems: typeof userItems = [];
  if (user?.role?.name === "admin") {
    roleItems = adminItems;
  } else if (user?.role?.name === "user") {
    roleItems = userItems;
  }

  // Only show role-based items
  const navItems = roleItems;

  const isActive = (path: string) => {
    // Exact match for home, partial match for other routes
    if (path === "/") {
      return location.pathname === path;
    }
    return location.pathname.startsWith(path);
  };

  return (
    <nav className="bg-card shadow-sm border-b-4 border-primary sticky top-0 z-10">
      <div className="max-w-[1400px] mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14 sm:h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link to="/management" className="flex items-center">
                <img
                  className="h-6 w-auto sm:h-8"
                  src="/logo.png"
                  alt="通知アラートロゴ"
                />
                <span className="ml-2 text-base sm:text-lg font-medium text-primary hidden xs:block">
                  通知アラート
                </span>
              </Link>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="flex items-center space-x-3 lg:space-x-6">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={cn(
                    "px-2 lg:px-3 py-2 text-sm font-medium transition-colors relative",
                    isActive(item.path)
                      ? "text-primary"
                      : "text-foreground hover:text-primary hover:bg-accent rounded-md"
                  )}
                >
                  {item.name}
                  {isActive(item.path) && (
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-primary" />
                  )}
                </Link>
              ))}

              {user ? (
                <div className="relative" ref={userMenuRef}>
                  <button
                    className="flex items-center justify-center w-9 h-9 rounded-full bg-primary text-primary-foreground focus:outline-none focus:ring-2 focus:ring-primary/50"
                    onClick={toggleUserMenu}
                    aria-label="ユーザーメニューを開く"
                  >
                    <span className="text-lg font-bold">
                      {user.full_name.charAt(0)}
                    </span>
                  </button>
                  {isUserMenuOpen && (
                    <div className="absolute right-0 mt-2 w-44 bg-card border border-border rounded-lg shadow-lg z-20">
                      <Link
                        to="/profile"
                        className="flex items-center px-4 py-2 text-sm hover:bg-accent/50 rounded-t-lg"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="mr-2 h-4 w-4" /> プロフィール
                      </Link>
                      {user.role.name === "admin" && (
                        <Link
                          to="/user-management"
                          className="flex items-center px-4 py-2 text-sm hover:bg-accent/50"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings className="mr-2 h-4 w-4" /> ユーザ管理
                        </Link>
                      )}
                      <Link
                        to="/change-password"
                        className="flex items-center px-4 py-2 text-sm hover:bg-accent/50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="mr-2 h-4 w-4" /> パスワード変更
                      </Link>
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-destructive hover:bg-accent/50 rounded-b-lg"
                        onClick={() => {
                          setIsUserMenuOpen(false);
                          dispatch(logout());
                          toast.success("ログアウトしました");
                        }}
                      >
                        <LogOut className="mr-2 h-4 w-4" /> ログアウト
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <Button
                    variant="default"
                    size="sm"
                    className="rounded-md"
                    asChild
                  >
                    <Link to="/login">ログイン</Link>
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              onClick={toggleMenu}
              variant="ghost"
              size="sm"
              className="p-1 h-auto"
              aria-expanded={isMenuOpen}
              aria-label="ナビゲーションメニューの切り替え"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div
        className={cn(
          "md:hidden absolute w-full bg-card border-t shadow-lg transition-all duration-300 ease-in-out",
          isMenuOpen
            ? "max-h-[400px] opacity-100 visible"
            : "max-h-0 opacity-0 invisible overflow-hidden"
        )}
      >
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "block px-3 py-2 rounded-md text-sm font-medium transition-colors relative",
                isActive(item.path)
                  ? "text-primary bg-primary/5"
                  : "text-foreground hover:text-primary hover:bg-accent"
              )}
            >
              {item.name}
            </Link>
          ))}

          {user ? (
            <>
              <div className="px-3 py-2 border-t mt-2 pt-2">
                <div className="flex items-center space-x-2 mb-1">
                  <div className="bg-primary text-primary-foreground w-8 h-8 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {user.full_name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm font-medium">{user.full_name}</p>
                    <p className="text-xs text-muted-foreground">
                      {user.role.name}
                    </p>
                  </div>
                </div>

                <Link
                  to="/profile"
                  className="flex items-center px-2 py-2 text-sm hover:bg-accent/50 rounded-md"
                >
                  <User className="mr-2 h-4 w-4" />
                  プロフィール
                </Link>
                <button
                  className="flex w-full items-center px-2 py-2 text-sm text-destructive hover:bg-accent/50 rounded-md"
                  onClick={() => {
                    dispatch(logout());
                    toast.success("ログアウトしました");
                  }}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  ログアウト
                </button>
              </div>
            </>
          ) : (
            <div className="px-3 py-2">
              <Button variant="default" size="sm" className="w-full" asChild>
                <Link to="/login">ログイン</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
