import { cn } from "@/lib/utils";
import { useGetStatusMutation } from "@/redux/features/device/deviceApi";
import React from "react";

interface DeviceStatusProps {
  deviceId: string;
  workId?: number;
  defaultStatus?: string;
}

/**
 * DeviceStatus component to display the current status of a device
 *
 * @param deviceId - The display ID of the device
 * @param workId - The associated work ID (optional)
 * @param defaultStatus - Default status to display if unavailable (optional)
 */
const DeviceStatus: React.FC<DeviceStatusProps> = ({
  deviceId,
  workId,
  defaultStatus = "-",
}) => {
  const [getStatus] = useGetStatusMutation();
  const [status, setStatus] = React.useState(defaultStatus);

  React.useEffect(() => {
    if (deviceId && workId !== undefined) {
      // Only call getStatus if both deviceId and workId are present
      const requestData = {
        deviceId: String(deviceId), // Ensure deviceId is a string
        workId: Number(workId), // Ensure workId is a number
      };

      getStatus(requestData)
        .unwrap()
        .then((data) => {
          if (data && data.status) {
            setStatus(data.status);
          }
        })
        .catch((error) => {
          console.error("Failed to get device status:", error);
          if (error.data) {
            console.error("Error data:", error.data);
            // If the device is not found, keep the default status
            if (
              error.data.message &&
              error.data.message.includes("not found")
            ) {
              console.log("Using default status:", defaultStatus);
            }
          }
        });
    }
  }, [deviceId, workId, getStatus, defaultStatus]);

  return (
    <>
      <span
        className={cn(
          "inline-block w-2 sm:w-3 h-2 sm:h-3 rounded-full mr-1 sm:mr-2",
          status === "normal"
            ? "bg-gray-400"
            : status === "waiting"
            ? "bg-orange-500"
            : status === "areaChange"
            ? "bg-blue-200"
            : status === "unstableLocation"
            ? "bg-purple-500"
            : status === "prohibitedAreaApproach"
            ? "bg-yellow-500"
            : status === "prohibitedAreaEnter"
            ? "bg-red-500"
            : "bg-gray-400"
        )}
      />
      {status === "normal"
        ? "アラート無し"
        : status === "waiting"
        ? "サーバー応答待ち"
        : status === "areaChange"
        ? "エリア変更"
        : status === "unstableLocation"
        ? "位置情報不安定"
        : status === "prohibitedAreaApproach"
        ? "進入禁止エリア接近"
        : status === "prohibitedAreaEnter"
        ? "進入禁止エリア進入"
        : "不明"}
    </>
  );
};

export default DeviceStatus;
