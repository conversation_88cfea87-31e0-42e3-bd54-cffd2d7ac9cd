import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Device,
  useCreateDeviceMutation,
  useDeleteDeviceMutation,
  useUpdateDeviceMutation,
} from "../redux/features/device/deviceApi";

interface DeviceManagementModalProps {
  visible: boolean;
  onClose: () => void;
  mode: "add" | "edit" | "delete" | "view";
  device?: Device;
}

const DeviceManagementModal: React.FC<DeviceManagementModalProps> = ({
  visible,
  onClose,
  mode,
  device,
}) => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      device_name: device?.device_name || "",
      display_device_id: device?.display_device_id || "",
      battery: device?.battery || 0,
      signal_period: device?.signal_period || 0,
    },
  });

  const [createDevice, { isLoading: isCreating }] = useCreateDeviceMutation();
  const [updateDevice, { isLoading: isUpdating }] = useUpdateDeviceMutation();
  const [deleteDevice, { isLoading: isDeleting }] = useDeleteDeviceMutation();

  React.useEffect(() => {
    if (visible && device && (mode === "edit" || mode === "view")) {
      reset({
        device_name: device.device_name,
        display_device_id: device.display_device_id,
        battery: device.battery,
        signal_period: device.signal_period,
      });
    } else if (visible && mode === "add") {
      reset({
        device_name: "",
        display_device_id: "",
        battery: 0,
        signal_period: 0,
      });
    }
  }, [visible, device, mode, reset]);

  const onSubmit = async (data: {
    device_name: string;
    display_device_id: string;
    battery: number;
    signal_period: number;
  }) => {
    try {
      if (mode === "add") {
        await createDevice({
          name: data.device_name,
          deviceId: data.display_device_id,
          charge: data.battery,
          status: "active",
        }).unwrap();
        toast.success("デバイスが正常に追加されました");
      } else if (mode === "edit" && device) {
        await updateDevice({
          id: device.id,
          data: {
            name: data.device_name,
            charge: data.battery,
            status: "active",
          },
        }).unwrap();
        toast.success("デバイスが正常に更新されました");
      }
      reset();
      onClose();
    } catch (error) {
      console.error("デバイス操作に失敗しました:", error);
      toast.error(
        mode === "add"
          ? "デバイスの追加に失敗しました"
          : "デバイスの更新に失敗しました"
      );
    }
  };

  const handleDelete = async () => {
    if (!device) return;

    try {
      await deleteDevice(device.id).unwrap();
      toast.success("デバイスが正常に削除されました");
      onClose();
    } catch (error) {
      console.error("デバイスの削除に失敗しました:", error);
      toast.error("デバイスの削除に失敗しました");
    }
  };

  const handleCancel = () => {
    reset();
    onClose();
  };

  const getTitle = () => {
    switch (mode) {
      case "add":
        return "新しいデバイスを追加";
      case "edit":
        return "デバイスを編集";
      case "delete":
        return "デバイスを削除";
      case "view":
        return "デバイス詳細";
      default:
        return "デバイス管理";
    }
  };

  const getDescription = () => {
    if (mode === "delete") {
      return `デバイス "${device?.device_name}" を削除してもよろしいですか？この操作は元に戻せません。`;
    }
    return "";
  };

  const isReadOnly = mode === "view" || mode === "delete";
  const isLoading = isCreating || isUpdating || isDeleting;

  return (
    <Dialog open={visible} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl">{getTitle()}</DialogTitle>
          {mode === "delete" && (
            <DialogDescription className="text-destructive">
              {getDescription()}
            </DialogDescription>
          )}
        </DialogHeader>

        {mode === "delete" ? (
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              デバイスID: {device?.display_device_id}
            </p>
            <p className="text-sm text-muted-foreground">
              デバイス名: {device?.device_name}
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* Device Name */}
            <div className="space-y-2">
              <Label htmlFor="device_name">デバイス名</Label>
              <Input
                id="device_name"
                placeholder="デバイス名を入力してください"
                readOnly={isReadOnly}
                {...register("device_name", {
                  required: "デバイス名は必須項目です",
                })}
              />
              {errors.device_name && (
                <p className="text-sm text-destructive">
                  {errors.device_name.message}
                </p>
              )}
            </div>

            {/* Display Device ID */}
            <div className="space-y-2">
              <Label htmlFor="display_device_id">デバイスID</Label>
              <Input
                id="display_device_id"
                placeholder="デバイスIDを入力してください"
                readOnly={isReadOnly || mode === "edit"}
                {...register("display_device_id", {
                  required: "デバイスIDは必須項目です",
                })}
              />
              {errors.display_device_id && (
                <p className="text-sm text-destructive">
                  {errors.display_device_id.message}
                </p>
              )}
              {mode === "edit" && (
                <p className="text-xs text-muted-foreground">
                  デバイスIDは編集できません
                </p>
              )}
            </div>

            {/* Battery */}
            <div className="space-y-2">
              <Label htmlFor="battery">バッテリー残量 (%)</Label>
              <Input
                id="battery"
                type="number"
                min="0"
                max="100"
                placeholder="バッテリー残量を入力してください"
                readOnly={isReadOnly}
                {...register("battery", {
                  required: "バッテリー残量は必須項目です",
                  min: {
                    value: 0,
                    message: "バッテリー残量は0以上である必要があります",
                  },
                  max: {
                    value: 100,
                    message: "バッテリー残量は100以下である必要があります",
                  },
                })}
              />
              {errors.battery && (
                <p className="text-sm text-destructive">
                  {errors.battery.message}
                </p>
              )}
            </div>

            {/* Signal Period */}
            <div className="space-y-2">
              <Label htmlFor="signal_period">信号間隔 (秒)</Label>
              <Input
                id="signal_period"
                type="number"
                min="1"
                placeholder="信号間隔を入力してください"
                readOnly={isReadOnly}
                {...register("signal_period", {
                  required: "信号間隔は必須項目です",
                  min: {
                    value: 1,
                    message: "信号間隔は1以上である必要があります",
                  },
                })}
              />
              {errors.signal_period && (
                <p className="text-sm text-destructive">
                  {errors.signal_period.message}
                </p>
              )}
            </div>
          </form>
        )}

        <DialogFooter className="mt-6">
          <Button type="button" variant="outline" onClick={handleCancel}>
            {mode === "view" ? "閉じる" : "キャンセル"}
          </Button>
          {mode === "delete" ? (
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading ? "削除中..." : "削除"}
            </Button>
          ) : mode !== "view" ? (
            <Button
              type="submit"
              disabled={isLoading}
              onClick={handleSubmit(onSubmit)}
            >
              {isLoading
                ? mode === "add"
                  ? "作成中..."
                  : "更新中..."
                : mode === "add"
                ? "デバイスを作成"
                : "デバイスを更新"}
            </Button>
          ) : null}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DeviceManagementModal;
