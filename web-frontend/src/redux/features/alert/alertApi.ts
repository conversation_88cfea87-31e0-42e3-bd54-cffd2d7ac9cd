import { baseApi } from "../../api/baseApi";

export interface Alert {
  id: string;
  device: string;
  alert_type: "approach" | "error" | "low_battery";
  message: string;
  is_read: boolean;
  created_at?: string;
}

export interface CreateAlertRequest {
  device: string;
  alert_type: string;
  message: string;
  is_read: boolean;
}

export interface UpdateAlertRequest {
  device?: string;
  alert_type?: string;
  message?: string;
  is_read?: boolean;
}

export const alertApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllAlerts: builder.query<Alert[], void>({
      query: () => "/alerts/",
      providesTags: ["Alert"],
    }),

    getAlertById: builder.query<Alert, string>({
      query: (id) => `/alerts/${id}/`,
      providesTags: (result, error, id) => [{ type: "Alert", id }],
    }),

    createAlert: builder.mutation<Alert, CreateAlertRequest>({
      query: (alert) => ({
        url: "/alerts/",
        method: "POST",
        body: alert,
      }),
      invalidatesTags: ["Alert"],
    }),

    updateAlert: builder.mutation<
      Alert,
      { id: string; data: UpdateAlertRequest }
    >({
      query: ({ id, data }) => ({
        url: `/alerts/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Alert", id }],
    }),

    deleteAlert: builder.mutation<void, string>({
      query: (id) => ({
        url: `/alerts/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["Alert"],
    }),

    markAlertAsRead: builder.mutation<void, string>({
      query: (id) => ({
        url: `/alerts/${id}/mark-read/`,
        method: "POST",
      }),
      invalidatesTags: (result, error, id) => [{ type: "Alert", id }],
    }),
  }),
});

export const {
  useGetAllAlertsQuery,
  useGetAlertByIdQuery,
  useCreateAlertMutation,
  useUpdateAlertMutation,
  useDeleteAlertMutation,
  useMarkAlertAsReadMutation,
} = alertApi;
