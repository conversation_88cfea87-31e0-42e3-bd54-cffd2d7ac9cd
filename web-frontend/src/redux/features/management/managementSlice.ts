import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../../store";
import { WorkGroup } from "../work/workApi";

interface ManagementState {
  selectedWork: string;
  selectedWorks: string[];
  workDetails: Record<string, WorkGroup>;
}

const initialState: ManagementState = {
  selectedWork: "",
  selectedWorks: [],
  workDetails: {},
};

export const managementSlice = createSlice({
  name: "management",
  initialState,
  reducers: {
    setSelectedWork: (state, action: PayloadAction<string>) => {
      state.selectedWork = action.payload;
      if (action.payload && !state.selectedWorks.includes(action.payload)) {
        state.selectedWorks.push(action.payload);
      }
    },
    removeSelectedWork: (state, action: PayloadAction<string>) => {
      state.selectedWorks = state.selectedWorks.filter(
        (workId) => workId !== action.payload
      );
      if (state.selectedWork === action.payload) {
        state.selectedWork = state.selectedWorks[0] || "";
      }
      // Remove from workDetails
      const newWorkDetails = { ...state.workDetails };
      delete newWorkDetails[action.payload];
      state.workDetails = newWorkDetails;
    },
    addWorkDetails: (
      state,
      action: PayloadAction<{ workId: string; details: WorkGroup }>
    ) => {
      state.workDetails = {
        ...state.workDetails,
        [action.payload.workId]: action.payload.details,
      };
    },
    clearSelectedWork: (state) => {
      state.selectedWork = "";
      state.selectedWorks = [];
      state.workDetails = {};
    },
    setSelectedWorks: (state, action: PayloadAction<string[]>) => {
      state.selectedWorks = action.payload;
      // If current selectedWork is not in the new array, update it
      if (!action.payload.includes(state.selectedWork)) {
        state.selectedWork = action.payload[0] || "";
      }
    },
  },
});

export const {
  setSelectedWork,
  removeSelectedWork,
  addWorkDetails,
  clearSelectedWork,
  setSelectedWorks,
} = managementSlice.actions;

export const selectSelectedWork = (state: RootState) =>
  state.management.selectedWork;

export const selectSelectedWorks = (state: RootState) =>
  state.management.selectedWorks;

export const selectWorkDetails = (state: RootState) =>
  state.management.workDetails;

export default managementSlice.reducer;
