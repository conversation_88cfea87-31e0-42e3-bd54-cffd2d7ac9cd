import { baseApi } from "../../api/baseApi";

export interface Work {
  id: string;
  workName: string;
  group_num: string;
  assignedDevices?: number;
  createdAt: string;
  timeElapsed?: string;
  accessibleAreas?: number;
}

export interface CreateWorkRequest {
  work_name: string;
}

export interface UpdateWorkRequest {
  work_name?: string;
}

interface WorkResponse {
  success: boolean;
  data: {
    works: Work[];
  };
}
export interface WorkGroup {
  id: number;
  workName: string;
  groupNumber: string;
  createdAt: string;
  timeElapsed: string;
  assignedDevices: number;
  accessibleAreas: number;
}

interface WorkGroupResponse {
  success: boolean;
  data: WorkGroup;
}

export const workApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllWorks: builder.query<WorkResponse, void>({
      query: () => "/lipsapi/works/",
      providesTags: ["Work"],
    }),

    getWorkById: builder.query<WorkGroupResponse, string>({
      query: (workId) => `/lipsapi/works/${workId}/`,
      providesTags: ["Work"],
    }),

    createWork: builder.mutation<Work, CreateWorkRequest>({
      query: (work) => ({
        url: "/lipsapi/works/",
        method: "POST",
        body: work,
      }),
      invalidatesTags: ["Work"],
    }),

    updateWork: builder.mutation<Work, { id: string; data: UpdateWorkRequest }>(
      {
        query: ({ id, data }) => ({
          url: `/lipsapi/works/${id}/`,
          method: "PUT",
          body: data,
        }),
        invalidatesTags: (result, error, { id }) => [
          { type: "Work", id },
          "Work",
        ],
      }
    ),

    deleteWork: builder.mutation<void, string>({
      query: (id) => ({
        url: `/lipsapi/works/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["Work", "Device"],
    }),
  }),
});

export const {
  useGetAllWorksQuery,
  useGetWorkByIdQuery,
  useCreateWorkMutation,
  useUpdateWorkMutation,
  useDeleteWorkMutation,
} = workApi;
