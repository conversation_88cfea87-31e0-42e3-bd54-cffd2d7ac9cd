import { baseApi } from "../../api/baseApi";

export interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  role: { id: number; name: string };
  last_login?: string;
  phone_number: string;
  notes: string;
  company_name: string;
  device_id?: string;
}

export interface LoginResponse {
  status: boolean;
  access_token: string;
  refresh_token: string;
  data: User;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface TokenRequest {
  username: string;
  password: string;
}

export interface RefreshRequest {
  refresh: string;
}

export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: (credentials) => ({
        url: "/userapi/login/",
        method: "POST",
        body: credentials,
      }),
    }),
    getToken: builder.mutation<LoginResponse, TokenRequest>({
      query: (credentials) => ({
        url: "/userapi/token/",
        method: "POST",
        body: credentials,
      }),
    }),
    refreshToken: builder.mutation<LoginResponse, RefreshRequest>({
      query: (refresh) => ({
        url: "/userapi/refresh/",
        method: "POST",
        body: refresh,
      }),
    }),

    changePassword: builder.mutation({
      query: (data) => ({
        url: "/userapi/change-password/",
        method: "POST",
        body: data,
      }),
    }),
    forgotPassword: builder.mutation({
      query: (data) => ({
        url: "/userapi/forgot-password/",
        method: "POST",
        body: data,
      }),
    }),
    resetPassword: builder.mutation({
      query: (data) => ({
        url: "/userapi/reset-password/",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useGetTokenMutation,
  useRefreshTokenMutation,
  useChangePasswordMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} = authApi;
