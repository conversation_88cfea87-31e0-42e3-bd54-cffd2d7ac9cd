import { baseApi } from "@/redux/api/baseApi";

export interface ActiveWork {
  id: number;
  work_name: string;
  group_number: string;
  created_at: string;
  time_elapsed: string;
  assigned_devices: number;
  accessible_areas: number;
}

interface RequestBody {
  work_ids: number[];
}

interface ActiveWorkResponse {
  success: boolean;
  data: ActiveWork[];
}

export const activeWorkApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllActiveWorks: builder.query({
      query: () => "/lipsapi/works/active-works/",
      providesTags: ["ActiveWork"],
      transformResponse: (response) => {
        return response.data.map((work: ActiveWork) => ({
          id: work.id,
          workName: work.work_name,
          groupNumber: work.group_number,
          createdAt: work.created_at,
          timeElapsed: work.time_elapsed,
          assignedDevices: work.assigned_devices,
          accessibleAreas: work.accessible_areas,
        }));
      },
    }),

    getActiveWorkById: builder.query({
      query: (id: number) => `/lipsapi/works/active-works/${id}/`,
      providesTags: (result, error, id) => [{ type: "ActiveWork", id }],
      transformResponse: (response) => {
        return {
          id: response.data.id,
          workName: response.data.work_name,
          groupNumber: response.data.group_number,
          createdAt: response.created_at,
          timeElapsed: response.data.time_elapsed,
          assignedDevices: response.data.assigned_devices,
          accessibleAreas: response.data.accessible_areas,
        };
      },
    }),

    addActiveWorks: builder.mutation({
      query: (body: RequestBody) => ({
        url: "/lipsapi/works/active-works/",
        method: "POST",
        body,
      }),
      invalidatesTags: ["ActiveWork"],
    }),
    deleteActiveWork: builder.mutation({
      query: (id: number) => ({
        url: `/lipsapi/works/active-works/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["ActiveWork"],
    }),
  }),
});

export const {
  useGetAllActiveWorksQuery,
  useAddActiveWorksMutation,
  useDeleteActiveWorkMutation,
  useGetActiveWorkByIdQuery,
} = activeWorkApi;
