import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../../store";

interface DeviceSettingState {
  selectedDeviceId: string | null;
  isEditing: boolean;
}

const initialState: DeviceSettingState = {
  selectedDeviceId: null,
  isEditing: false,
};

export const deviceSettingSlice = createSlice({
  name: "deviceSetting",
  initialState,
  reducers: {
    setSelectedDeviceId: (state, action: PayloadAction<string | null>) => {
      state.selectedDeviceId = action.payload;
    },
    setIsEditing: (state, action: PayloadAction<boolean>) => {
      state.isEditing = action.payload;
    },
    resetDeviceSettings: (state) => {
      state.selectedDeviceId = null;
      state.isEditing = false;
    },
  },
});

export const { setSelectedDeviceId, setIsEditing, resetDeviceSettings } =
  deviceSettingSlice.actions;

export const selectSelectedDeviceId = (state: RootState) =>
  state.deviceSetting.selectedDeviceId;
export const selectIsEditing = (state: RootState) =>
  state.deviceSetting.isEditing;

export default deviceSettingSlice.reducer;
