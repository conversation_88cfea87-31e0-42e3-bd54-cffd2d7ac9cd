import { baseApi } from "../../api/baseApi";

export interface DeviceSetting {
  assignedAt: string;
  id: string;
  device: string;
  name: string;
  deviceId: string;
  approachDistance: number;
  signalPeriod: number;
  approachSeconds: number;
  status: string;
}

export interface UpdateDeviceSettingRequest {
  approachDistance?: number;
  approachSeconds?: number;
  status?: string;
  name?: string;
}

interface DeviceSettingResponse {
  success: boolean;
  data: {
    deviceSettings: DeviceSetting[];
  };
}
export const deviceSettingApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllDeviceSettings: builder.query<DeviceSettingResponse, void>({
      query: () => "/lipsapi/device/settings/",
      providesTags: ["DeviceSetting"],
    }),

    updateDeviceSetting: builder.mutation<
      DeviceSetting,
      { id: string; data: UpdateDeviceSettingRequest }
    >({
      query: ({ id, data }) => ({
        url: `/lipsapi/device/settings/${id}/`,
        method: "PUT",
        body: data,
      }),
      // Improve cache invalidation by targeting specific ID and the general tag
      invalidatesTags: (result, error, { id }) => [
        { type: "DeviceSetting", id },
        "DeviceSetting",
      ],
    }),
  }),
});

export const { useGetAllDeviceSettingsQuery, useUpdateDeviceSettingMutation } =
  deviceSettingApi;
