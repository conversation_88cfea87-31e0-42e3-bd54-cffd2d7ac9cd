import { baseApi } from "../../api/baseApi";

export interface Device {
  id: string;
  assignedWork: string;
  usageTime: string;
  display_device_id: string;
  device_name: string;
  work_time: string;
  battery: number;
  assignedAt: string;
  previous_alert_instruction: string;
  signal_period: number;
  user_id: string | number;
  work_id: number;
  status?: string;
}

export interface CreateDeviceRequest {
  name: string;
  deviceId: string;
  charge: number;
  status: string;
}

export interface UpdateDeviceRequest {
  name?: string;
  charge?: number;
  status?: string;
}

export interface DeviceAssignmentRequest {
  assignedWork: number;
  deviceId: string;
}

interface DeviceResponse {
  success: boolean;
  data: {
    devices: Device[];
  };
}

export const deviceApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllDevices: builder.query<DeviceResponse, void>({
      query: () => "/lipsapi/devices/",
      providesTags: ["Device"],
    }),

    getDeviceById: builder.query<Device, string>({
      query: (id) => `/lipsapi/device/${id}/`,
      providesTags: (result, error, id) => [{ type: "Device", id }],
    }),

    createDevice: builder.mutation<Device, CreateDeviceRequest>({
      query: (device) => ({
        url: "/lipsapi/device/",
        method: "POST",
        body: device,
      }),
      invalidatesTags: ["Device"],
    }),

    updateDevice: builder.mutation<
      Device,
      { id: string; data: UpdateDeviceRequest }
    >({
      query: ({ id, data }) => ({
        url: `/lipsapi/device/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Device", id }],
    }),

    deleteDevice: builder.mutation<void, string>({
      query: (id) => ({
        url: `/lipsapi/device/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["Device"],
    }),

    assignWork: builder.mutation<
      Device,
      { id: string; data: DeviceAssignmentRequest }
    >({
      query: ({ data }) => ({
        url: `/lipsapi/device/assign-work/`,
        method: "POST",
        body: data,
      }),

      invalidatesTags: (result, error, { id }) => [
        { type: "Device", id },
        "Device",
        "Work",
      ],
    }),

    removeWork: builder.mutation({
      query: ({ data }) => ({
        url: `/lipsapi/device/remove-work/`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "Device", id },
        "Device",
        "Work",
      ],
    }),
    getStatus: builder.mutation<
      { status: string },
      { deviceId: string; workId: number }
    >({
      query: (body) => ({
        url: `/lipsapi/device/status/`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, { deviceId }) => [
        { type: "Device", id: deviceId },
      ],
    }),
  }),
});

export const {
  useGetAllDevicesQuery,
  useGetDeviceByIdQuery,
  useCreateDeviceMutation,
  useUpdateDeviceMutation,
  useDeleteDeviceMutation,
  useAssignWorkMutation,
  useRemoveWorkMutation,
  useGetStatusMutation,
} = deviceApi;
