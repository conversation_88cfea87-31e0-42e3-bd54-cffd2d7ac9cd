import { baseApi } from "../../api/baseApi";

const userManagementApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getAllUsers: builder.query({
      query: () => ({
        url: "/userapi/users/list/",
        method: "GET",
      }),
      providesTags: ["User"],
    }),
    updateUserById: builder.mutation({
      query: ({ id, ...data }) => ({
        url: `/userapi/users/update/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["User"],
    }),
    addUser: builder.mutation({
      query: (data) => ({
        url: "/userapi/users/add/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["User"],
    }),
    deleteUser: builder.mutation({
      query: (id) => ({
        url: `/userapi/users/${id}/delete/`,
        method: "DELETE",
      }),
      invalidatesTags: ["User"],
    }),
  }),
});

export const {
  useGetAllUsersQuery,
  useUpdateUserByIdMutation,
  useAddUserMutation,
  useDeleteUserMutation,
} = userManagementApi;
