import { configureStore } from "@reduxjs/toolkit";
import { persistStore } from "redux-persist";
import { baseApi } from "./api/baseApi";
import { persistedReducer } from "./persistConfig";

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }).concat(baseApi.middleware),
});

export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export user selector for easy access
export const selectUser = (state: RootState) => state.auth.user;
