import {
  BaseQueryFn,
  create<PERSON><PERSON>,
  Fetch<PERSON>rgs,
  fetchBase<PERSON>uery,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";
import { toast } from "sonner";
import { logout, updateToken } from "../features/auth/authSlice";
import { RootState } from "../store";

const baseQuery = fetchBaseQuery({
  baseUrl: import.meta.env.VITE_API_URL as string,
  prepareHeaders: (headers, { getState }) => {
    const token = (getState() as RootState).auth.accessToken;
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

export const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === 401) {
    // Try to get a new token
    const refreshToken = (api.getState() as RootState).auth.refreshToken;

    if (refreshToken) {
      // Try to refresh token
      const refreshResult = await baseQuery(
        {
          url: "/userapi/refresh/",
          method: "POST",
          body: { refresh: refreshToken },
        },
        api,
        extraOptions
      );

      if (refreshResult.data) {
        // Store the new token
        const { access_token, refresh_token } = refreshResult.data as {
          access_token: string;
          refresh_token: string;
        };
        api.dispatch(updateToken({ access_token, refresh_token }));

        // Retry the original request
        result = await baseQuery(args, api, extraOptions);
      } else {
        // Refresh failed, logout user
        api.dispatch(logout());
        toast.error("Session expired. Please log in again.");
      }
    } else {
      api.dispatch(logout());
      toast.error("Check credentials and try again.");
    }
  }

  // Handle other errors
  if (result.error?.status === 404) {
    console.log("Resource not found", result.error);
  }
  if (result.error?.status === 403) {
    console.log(
      (result?.error?.data as { message?: string })?.message || "Forbidden"
    );
  }

  return result;
};

export const baseApi = createApi({
  reducerPath: "api",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["User", "Device", "DeviceSetting", "Work", "Alert", "ActiveWork"],
  endpoints: () => ({}),
});
