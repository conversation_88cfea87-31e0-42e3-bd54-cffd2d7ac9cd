import { combineReducers } from "@reduxjs/toolkit";
import { persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { baseApi } from "./api/baseApi";
import authReducer from "./features/auth/authSlice";
import deviceSettingReducer from "./features/device-setting/deviceSettingSlice";
import managementReducer from "./features/management/managementSlice";

const persistConfig = {
  key: "auth",
  storage,
  whitelist: ["auth", "management"], // also persist management slice
};

const rootReducer = combineReducers({
  [baseApi.reducerPath]: baseApi.reducer,
  auth: authReducer,
  deviceSetting: deviceSettingReducer,
  management: managementReducer,
});

export const persistedReducer = persistReducer(persistConfig, rootReducer);
