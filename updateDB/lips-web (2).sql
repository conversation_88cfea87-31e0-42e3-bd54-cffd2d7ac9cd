-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 23, 2025 at 06:46 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.1.25

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `lips-web`
--

-- --------------------------------------------------------

--
-- Table structure for table `auth_group`
--

CREATE TABLE `auth_group` (
  `id` int(11) NOT NULL,
  `name` varchar(150) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `auth_group_permissions`
--

CREATE TABLE `auth_group_permissions` (
  `id` bigint(20) NOT NULL,
  `group_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `auth_permission`
--

CREATE TABLE `auth_permission` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `content_type_id` int(11) NOT NULL,
  `codename` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `auth_permission`
--

INSERT INTO `auth_permission` (`id`, `name`, `content_type_id`, `codename`) VALUES
(1, 'Can add log entry', 1, 'add_logentry'),
(2, 'Can change log entry', 1, 'change_logentry'),
(3, 'Can delete log entry', 1, 'delete_logentry'),
(4, 'Can view log entry', 1, 'view_logentry'),
(5, 'Can add permission', 2, 'add_permission'),
(6, 'Can change permission', 2, 'change_permission'),
(7, 'Can delete permission', 2, 'delete_permission'),
(8, 'Can view permission', 2, 'view_permission'),
(9, 'Can add group', 3, 'add_group'),
(10, 'Can change group', 3, 'change_group'),
(11, 'Can delete group', 3, 'delete_group'),
(12, 'Can view group', 3, 'view_group'),
(13, 'Can add content type', 4, 'add_contenttype'),
(14, 'Can change content type', 4, 'change_contenttype'),
(15, 'Can delete content type', 4, 'delete_contenttype'),
(16, 'Can view content type', 4, 'view_contenttype'),
(17, 'Can add session', 5, 'add_session'),
(18, 'Can change session', 5, 'change_session'),
(19, 'Can delete session', 5, 'delete_session'),
(20, 'Can view session', 5, 'view_session'),
(21, 'Can add role', 6, 'add_role'),
(22, 'Can change role', 6, 'change_role'),
(23, 'Can delete role', 6, 'delete_role'),
(24, 'Can view role', 6, 'view_role'),
(25, 'Can add user', 7, 'add_user'),
(26, 'Can change user', 7, 'change_user'),
(27, 'Can delete user', 7, 'delete_user'),
(28, 'Can view user', 7, 'view_user'),
(29, 'Can add alert instruction management info', 8, 'add_alertinstructionmanagementinfo'),
(30, 'Can change alert instruction management info', 8, 'change_alertinstructionmanagementinfo'),
(31, 'Can delete alert instruction management info', 8, 'delete_alertinstructionmanagementinfo'),
(32, 'Can view alert instruction management info', 8, 'view_alertinstructionmanagementinfo'),
(33, 'Can add device master', 9, 'add_devicemaster'),
(34, 'Can change device master', 9, 'change_devicemaster'),
(35, 'Can delete device master', 9, 'delete_devicemaster'),
(36, 'Can view device master', 9, 'view_devicemaster'),
(37, 'Can add fix info', 10, 'add_fixinfo'),
(38, 'Can change fix info', 10, 'change_fixinfo'),
(39, 'Can delete fix info', 10, 'delete_fixinfo'),
(40, 'Can view fix info', 10, 'view_fixinfo'),
(41, 'Can add prohibited approach info', 11, 'add_prohibitedapproachinfo'),
(42, 'Can change prohibited approach info', 11, 'change_prohibitedapproachinfo'),
(43, 'Can delete prohibited approach info', 11, 'delete_prohibitedapproachinfo'),
(44, 'Can view prohibited approach info', 11, 'view_prohibitedapproachinfo'),
(45, 'Can add settings info', 12, 'add_settingsinfo'),
(46, 'Can change settings info', 12, 'change_settingsinfo'),
(47, 'Can delete settings info', 12, 'delete_settingsinfo'),
(48, 'Can view settings info', 12, 'view_settingsinfo'),
(49, 'Can add user master', 13, 'add_usermaster'),
(50, 'Can change user master', 13, 'change_usermaster'),
(51, 'Can delete user master', 13, 'delete_usermaster'),
(52, 'Can view user master', 13, 'view_usermaster'),
(53, 'Can add work info', 14, 'add_workinfo'),
(54, 'Can change work info', 14, 'change_workinfo'),
(55, 'Can delete work info', 14, 'delete_workinfo'),
(56, 'Can view work info', 14, 'view_workinfo'),
(57, 'Can add permited approach info', 15, 'add_permitedapproachinfo'),
(58, 'Can change permited approach info', 15, 'change_permitedapproachinfo'),
(59, 'Can delete permited approach info', 15, 'delete_permitedapproachinfo'),
(60, 'Can view permited approach info', 15, 'view_permitedapproachinfo'),
(61, 'Can add historical judge alert instruction', 16, 'add_historicaljudgealertinstruction'),
(62, 'Can change historical judge alert instruction', 16, 'change_historicaljudgealertinstruction'),
(63, 'Can delete historical judge alert instruction', 16, 'delete_historicaljudgealertinstruction'),
(64, 'Can view historical judge alert instruction', 16, 'view_historicaljudgealertinstruction'),
(65, 'Can add device receive data', 17, 'add_devicereceivedata'),
(66, 'Can change device receive data', 17, 'change_devicereceivedata'),
(67, 'Can delete device receive data', 17, 'delete_devicereceivedata'),
(68, 'Can view device receive data', 17, 'view_devicereceivedata'),
(69, 'Can add Active Work', 18, 'add_activework'),
(70, 'Can change Active Work', 18, 'change_activework'),
(71, 'Can delete Active Work', 18, 'delete_activework'),
(72, 'Can view Active Work', 18, 'view_activework'),
(73, 'Can add business operator master', 19, 'add_businessoperatormaster'),
(74, 'Can change business operator master', 19, 'change_businessoperatormaster'),
(75, 'Can delete business operator master', 19, 'delete_businessoperatormaster'),
(76, 'Can view business operator master', 19, 'view_businessoperatormaster');

-- --------------------------------------------------------

--
-- Table structure for table `django_admin_log`
--

CREATE TABLE `django_admin_log` (
  `id` int(11) NOT NULL,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext DEFAULT NULL,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint(5) UNSIGNED NOT NULL CHECK (`action_flag` >= 0),
  `change_message` longtext NOT NULL,
  `content_type_id` int(11) DEFAULT NULL,
  `user_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `django_content_type`
--

CREATE TABLE `django_content_type` (
  `id` int(11) NOT NULL,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `django_content_type`
--

INSERT INTO `django_content_type` (`id`, `app_label`, `model`) VALUES
(1, 'admin', 'logentry'),
(3, 'auth', 'group'),
(2, 'auth', 'permission'),
(4, 'contenttypes', 'contenttype'),
(18, 'lips', 'activework'),
(8, 'lips', 'alertinstructionmanagementinfo'),
(19, 'lips', 'businessoperatormaster'),
(9, 'lips', 'devicemaster'),
(17, 'lips', 'devicereceivedata'),
(10, 'lips', 'fixinfo'),
(16, 'lips', 'historicaljudgealertinstruction'),
(15, 'lips', 'permitedapproachinfo'),
(11, 'lips', 'prohibitedapproachinfo'),
(12, 'lips', 'settingsinfo'),
(13, 'lips', 'usermaster'),
(14, 'lips', 'workinfo'),
(5, 'sessions', 'session'),
(6, 'usermanagement', 'role'),
(7, 'usermanagement', 'user');

-- --------------------------------------------------------

--
-- Table structure for table `django_migrations`
--

CREATE TABLE `django_migrations` (
  `id` bigint(20) NOT NULL,
  `app` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `applied` datetime(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `django_migrations`
--

INSERT INTO `django_migrations` (`id`, `app`, `name`, `applied`) VALUES
(1, 'contenttypes', '0001_initial', '2025-06-23 02:12:38.870030'),
(2, 'contenttypes', '0002_remove_content_type_name', '2025-06-23 02:12:38.914803'),
(3, 'auth', '0001_initial', '2025-06-23 02:12:39.012747'),
(4, 'auth', '0002_alter_permission_name_max_length', '2025-06-23 02:12:39.048600'),
(5, 'auth', '0003_alter_user_email_max_length', '2025-06-23 02:12:39.057444'),
(6, 'auth', '0004_alter_user_username_opts', '2025-06-23 02:12:39.064942'),
(7, 'auth', '0005_alter_user_last_login_null', '2025-06-23 02:12:39.076165'),
(8, 'auth', '0006_require_contenttypes_0002', '2025-06-23 02:12:39.081234'),
(9, 'auth', '0007_alter_validators_add_error_messages', '2025-06-23 02:12:39.088917'),
(10, 'auth', '0008_alter_user_username_max_length', '2025-06-23 02:12:39.096960'),
(11, 'auth', '0009_alter_user_last_name_max_length', '2025-06-23 02:12:39.106458'),
(12, 'auth', '0010_alter_group_name_max_length', '2025-06-23 02:12:39.106458'),
(13, 'auth', '0011_update_proxy_permissions', '2025-06-23 02:12:39.128221'),
(14, 'auth', '0012_alter_user_first_name_max_length', '2025-06-23 02:12:39.138106'),
(15, 'usermanagement', '0001_initial', '2025-06-23 02:12:39.253390'),
(16, 'admin', '0001_initial', '2025-06-23 02:12:39.291955'),
(17, 'admin', '0002_logentry_remove_auto_add', '2025-06-23 02:12:39.307275'),
(18, 'admin', '0003_logentry_add_action_flag_choices', '2025-06-23 02:12:39.309993'),
(19, 'lips', '0001_initial', '2025-06-23 02:12:39.533728'),
(20, 'lips', '0002_auto_convert_ids', '2025-06-23 02:12:40.152732'),
(21, 'lips', '0002_alter_devicemaster_id_alter_workinfo_id', '2025-06-23 02:12:40.656491'),
(22, 'lips', '0003_merge_20250511_1802', '2025-06-23 02:12:40.661171'),
(23, 'lips', '0004_recreate_tables', '2025-06-23 02:12:40.912018'),
(24, 'lips', '0005_auto_20250511_1809', '2025-06-23 02:12:40.927643'),
(25, 'lips', '0006_create_device_receive_data_table', '2025-06-23 02:12:40.954994'),
(26, 'lips', '0007_alter_devicemaster_id_alter_workinfo_id', '2025-06-23 02:12:41.005374'),
(27, 'lips', '0008_auto_fix_mysql_tables', '2025-06-23 02:12:41.022303'),
(28, 'lips', '0009_alter_devicemaster_created_at', '2025-06-23 02:12:41.088681'),
(29, 'lips', '0006_alter_devicemaster_id_alter_workinfo_id', '2025-06-23 02:12:41.424662'),
(30, 'lips', '0010_merge_20250512_1709', '2025-06-23 02:12:41.424662'),
(31, 'lips', '0011_alter_devicemaster_id_alter_workinfo_id', '2025-06-23 02:12:41.958048'),
(32, 'lips', '0010_devicemaster_status', '2025-06-23 02:12:41.958048'),
(33, 'lips', '0012_merge_20250515_1738', '2025-06-23 02:12:41.976508'),
(34, 'lips', '0011_merge_20250517_0553', '2025-06-23 02:12:41.989740'),
(35, 'lips', '0005_fix_database_structure', '2025-06-23 02:12:42.098654'),
(36, 'lips', '0013_merge_20250517_0742', '2025-06-23 02:12:42.101870'),
(37, 'lips', '0012_fix_database_structure', '2025-06-23 02:12:42.207673'),
(38, 'lips', '0013_devicemaster_approach_area_distance_and_more', '2025-06-23 02:12:42.241266'),
(39, 'lips', '0014_activework_workinfo_time_elapsed', '2025-06-23 02:12:42.264134'),
(40, 'lips', '0015_alter_devicemaster_updated_at', '2025-06-23 02:12:42.314659'),
(41, 'lips', '0016_businessoperatormaster', '2025-06-23 02:12:42.331841'),
(42, 'lips', '0017_devicemaster_assigned_at', '2025-06-23 02:12:42.345912'),
(43, 'sessions', '0001_initial', '2025-06-23 02:12:42.373789'),
(44, 'usermanagement', '0002_remove_user_company_id_user_company_name', '2025-06-23 02:12:42.410274'),
(45, 'usermanagement', '0003_alter_user_company_name', '2025-06-23 02:12:42.459182'),
(46, 'usermanagement', '0004_alter_user_company_name', '2025-06-23 02:12:42.512785'),
(47, 'usermanagement', '0005_alter_user_company_name', '2025-06-23 02:12:42.548074'),
(48, 'usermanagement', '0006_alter_user_company_name', '2025-06-23 02:12:42.591663'),
(49, 'usermanagement', '0007_rename_address_user_notes', '2025-06-23 02:12:42.612580');

-- --------------------------------------------------------

--
-- Table structure for table `django_session`
--

CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `django_session`
--

INSERT INTO `django_session` (`session_key`, `session_data`, `expire_date`) VALUES
('d3fb41dcaz06rol3bi0q2enh1zz7sqe1', '.eJxVjMEOwiAQRP-FsyG0sIAevfcbCOwuUjU0Ke3J-O9K0oMe5jLvzbxEiPtWwt54DTOJixi0OP2WKeKDayd0j_W2SFzqts5JdkUetMlpIX5eD_fvoMRWvuuoAUZg5RxmpRVY59iMPuOAQKR1DxrQyN64TD4h0Zmz5QzWJx_F-wMAKTiq:1uTWmb:0rM7g4aX8h9SOntlwvaw5mcSecWGhi_yBH0NetjDX0g', '2025-07-07 02:19:41.133924'),
('kzqnzga1xzksw2p6eiisr5t6w0ilunol', '.eJxVjMEOwiAQRP-FsyG0sIAevfcbCOwuUjU0Ke3J-O9K0oMe5jLvzbxEiPtWwt54DTOJixi0OP2WKeKDayd0j_W2SFzqts5JdkUetMlpIX5eD_fvoMRWvuuoAUZg5RxmpRVY59iMPuOAQKR1DxrQyN64TD4h0Zmz5QzWJx_F-wMAKTiq:1uTWmS:sOpKZ_EJ1MRlZaLTljAiyTn3yWR2N9Ysm0AImfiJpAo', '2025-07-07 02:19:32.192576');

-- --------------------------------------------------------

--
-- Table structure for table `lips_active_work`
--

CREATE TABLE `lips_active_work` (
  `id` bigint(20) NOT NULL,
  `work_name` varchar(255) NOT NULL,
  `group_number` varchar(100) DEFAULT NULL,
  `assigned_devices` int(11) NOT NULL,
  `time_elapsed` varchar(50) DEFAULT NULL,
  `accessible_areas` int(11) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_active_work`
--

INSERT INTO `lips_active_work` (`id`, `work_name`, `group_number`, `assigned_devices`, `time_elapsed`, `accessible_areas`, `created_at`, `updated_at`) VALUES
(1, 'SVLR0008', '1111', 1, '07:48:59.037', 1, '2025-06-23 02:19:41.326025', '2025-06-23 02:25:31.647464'),
(2, 'SVLR0010', '1112', 2, '07:48:59.037', 1, '2025-06-23 02:19:41.372709', '2025-06-23 02:25:31.671734'),
(3, 'SVLR0012', '2111', 1, '07:48:59.037', 1, '2025-06-23 02:19:41.386598', '2025-06-23 02:25:31.684801'),
(4, 'SVLR0015', '3111', 1, '07:48:59.037', 1, '2025-06-23 02:19:41.415481', '2025-06-23 02:25:31.696087'),
(5, 'SVLR0018', '4111', 0, '07:48:59.037', 1, '2025-06-23 02:19:41.418989', '2025-06-23 02:25:31.707566');

-- --------------------------------------------------------

--
-- Table structure for table `lips_alert_instruction_management_info`
--

CREATE TABLE `lips_alert_instruction_management_info` (
  `id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_alert_instruction_management_info`
--

INSERT INTO `lips_alert_instruction_management_info` (`id`) VALUES
(1),
(2),
(3),
(4),
(5);

-- --------------------------------------------------------

--
-- Table structure for table `lips_business_operator_masters`
--

CREATE TABLE `lips_business_operator_masters` (
  `id` int(11) NOT NULL,
  `business_operator_name` varchar(255) NOT NULL,
  `domain_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_business_operator_masters`
--

INSERT INTO `lips_business_operator_masters` (`id`, `business_operator_name`, `domain_name`) VALUES
(1, 'ABC Corp', '************'),
(2, 'Itage Co', '*************'),
(3, 'ITAGE GLOBAL Co', '************'),
(4, 'XYZ Ltd', '************'),
(5, 'Global Solutions', '************'),
(6, 'Tech Innovators', '*************'),
(7, 'Web Masters', '*************');

-- --------------------------------------------------------

--
-- Table structure for table `lips_device_masters`
--

CREATE TABLE `lips_device_masters` (
  `id` bigint(20) NOT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `display_device_id` varchar(100) DEFAULT NULL,
  `device_name` varchar(100) DEFAULT NULL,
  `work_id` bigint(20) DEFAULT NULL,
  `work_time` varchar(100) DEFAULT NULL,
  `battery` bigint(20) DEFAULT NULL,
  `previous_alert_instruction` varchar(100) DEFAULT NULL,
  `signal_period` bigint(20) DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `status` varchar(100) DEFAULT NULL,
  `approach_area_distance` double DEFAULT NULL,
  `approach_area_seconds` int(11) DEFAULT NULL,
  `assigned_at` datetime(6) DEFAULT NULL,
  `business_operator_id` int(20) NOT NULL,
  `approval_status` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_device_masters`
--

INSERT INTO `lips_device_masters` (`id`, `user_id`, `display_device_id`, `device_name`, `work_id`, `work_time`, `battery`, `previous_alert_instruction`, `signal_period`, `created_at`, `updated_at`, `status`, `approach_area_distance`, `approach_area_seconds`, `assigned_at`, `business_operator_id`, `approval_status`) VALUES
(1, 'USER001', '0010', 'motomachi gg', 2, '02:30:00', 85, '0', 30, '2025-06-23 02:12:43.856151', '2025-06-23 02:24:50.051287', 'waiting', NULL, NULL, '2025-06-23 02:24:33.441571', 1, 'Rejected'),
(2, 'USER002', '0020', 'shibuya', 1, '03:45:00', 65, '0', 45, '2025-06-23 02:12:43.861527', '2025-06-23 02:24:35.359717', 'areaChange', NULL, NULL, '2025-06-23 02:24:35.359717', 2, 'Rejected'),
(3, 'USER003', '0030', 'akihabara', 2, '05:20:00', 25, '1', 60, '2025-06-23 02:12:43.874469', '2025-06-23 02:19:56.003645', 'unstableLocation', NULL, NULL, '2025-06-23 02:19:56.003645', 3, 'Approved'),
(4, 'USER001', '0040', 'shinjuku', 3, '01:10:00', 10, '1', 30, '2025-06-23 02:12:43.877631', '2025-06-23 04:27:34.026315', 'prohibitedAreaApproach', NULL, NULL, '2025-06-23 02:19:59.255399', 4, 'Rejected'),
(5, 'USER002', '0050', 'tokyo', 4, '00:45:00', 90, '0', 45, '2025-06-23 02:12:43.885836', '2025-06-23 02:20:01.524630', 'prohibitedAreaEnter', NULL, NULL, '2025-06-23 02:20:01.524630', 5, 'Approved');

-- --------------------------------------------------------

--
-- Table structure for table `lips_device_receive_data`
--

CREATE TABLE `lips_device_receive_data` (
  `id` varchar(100) NOT NULL,
  `device_id` bigint(20) DEFAULT NULL,
  `time` varchar(100) DEFAULT NULL,
  `previous_alert_id` varchar(100) DEFAULT NULL,
  `battery` bigint(20) DEFAULT NULL,
  `dop` bigint(20) DEFAULT NULL,
  `ns_latitude_identifier` varchar(100) DEFAULT NULL,
  `latitude` bigint(20) DEFAULT NULL,
  `ew_longitude_identifier` varchar(100) DEFAULT NULL,
  `longitude` bigint(20) DEFAULT NULL,
  `x_acceleration` bigint(20) DEFAULT NULL,
  `y_acceleration` bigint(20) DEFAULT NULL,
  `z_acceleration` bigint(20) DEFAULT NULL,
  `alive_count` bigint(20) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_device_receive_data`
--

INSERT INTO `lips_device_receive_data` (`id`, `device_id`, `time`, `previous_alert_id`, `battery`, `dop`, `ns_latitude_identifier`, `latitude`, `ew_longitude_identifier`, `longitude`, `x_acceleration`, `y_acceleration`, `z_acceleration`, `alive_count`, `created_at`, `updated_at`) VALUES
('DRD001', 1, '2025-04-29 09:00:00', 'ALERT001', 85, 5, 'N', 356812, 'E', 1397671, 100, 200, 300, 1, '2025-04-29 09:00:00', '2025-04-29 09:00:00'),
('DRD002', 2, '2025-04-29 09:15:00', 'ALERT002', 65, 7, 'N', 356732, 'E', 1397601, 120, 220, 320, 2, '2025-04-29 09:15:00', '2025-04-29 09:15:00'),
('DRD003', 3, '2025-04-29 09:30:00', 'ALERT003', 25, 6, 'N', 356602, 'E', 1397521, 110, 210, 310, 3, '2025-04-29 09:30:00', '2025-04-29 09:30:00'),
('DRD004', 4, '2025-04-29 09:45:00', 'ALERT004', 10, 8, 'N', 356502, 'E', 1397421, 130, 230, 330, 4, '2025-04-29 09:45:00', '2025-04-29 09:45:00'),
('DRD005', 5, '2025-04-29 10:00:00', 'ALERT005', 90, 4, 'N', 356402, 'E', 1397321, 90, 190, 290, 5, '2025-04-29 10:00:00', '2025-04-29 10:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `lips_fix_info`
--

CREATE TABLE `lips_fix_info` (
  `id` varchar(100) NOT NULL,
  `key_name` varchar(100) DEFAULT NULL,
  `val` varchar(100) DEFAULT NULL,
  `summary` varchar(100) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_fix_info`
--

INSERT INTO `lips_fix_info` (`id`, `key_name`, `val`, `summary`, `created_at`, `updated_at`) VALUES
('FI001', 'approach_threshold', '5.0', 'Approach distance threshold', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('FI002', 'battery_threshold', '20', 'Battery warning threshold', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('FI003', 'signal_interval', '30', 'Signal interval in seconds', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('FI004', 'offline_timeout', '300', 'Offline timeout in seconds', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('FI005', 'alert_expiry', '3600', 'Alert expiry in seconds', '2025-06-23 02:12:44', '2025-06-23 02:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `lips_historical_judge_alert_instruction`
--

CREATE TABLE `lips_historical_judge_alert_instruction` (
  `id` varchar(100) NOT NULL,
  `device_id` bigint(20) DEFAULT NULL,
  `ns_latitude_identifier` varchar(100) DEFAULT NULL,
  `latitude` bigint(20) DEFAULT NULL,
  `ew_longitude_identifier` varchar(100) DEFAULT NULL,
  `longitude` bigint(20) DEFAULT NULL,
  `utm_x` varchar(100) DEFAULT NULL,
  `utm_y` varchar(100) DEFAULT NULL,
  `x_acceleration` bigint(20) DEFAULT NULL,
  `y_acceleration` bigint(20) DEFAULT NULL,
  `z_acceleration` bigint(20) DEFAULT NULL,
  `alert_instruction` bigint(20) DEFAULT NULL,
  `alert_id` varchar(100) DEFAULT NULL,
  `previous_alert_id` varchar(100) DEFAULT NULL,
  `work_time` varchar(100) DEFAULT NULL,
  `group_num` varchar(100) DEFAULT NULL,
  `alive_count` bigint(20) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_historical_judge_alert_instruction`
--

INSERT INTO `lips_historical_judge_alert_instruction` (`id`, `device_id`, `ns_latitude_identifier`, `latitude`, `ew_longitude_identifier`, `longitude`, `utm_x`, `utm_y`, `x_acceleration`, `y_acceleration`, `z_acceleration`, `alert_instruction`, `alert_id`, `previous_alert_id`, `work_time`, `group_num`, `alive_count`, `created_at`, `updated_at`) VALUES
('HJA001', 1, 'N', 356812, 'E', 1397671, '539750', '3938100', 100, 200, 300, 0, 'ALERT001', 'PREV001', '02:30:00', '1111', 1, '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('HJA002', 2, 'N', 356732, 'E', 1397601, '539700', '3938050', 120, 220, 320, 1, 'ALERT002', 'PREV002', '03:45:00', '1112', 2, '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('HJA003', 3, 'N', 356602, 'E', 1397521, '539650', '3938000', 110, 210, 310, 1, 'ALERT003', 'PREV003', '05:20:00', '2111', 3, '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('HJA004', 4, 'N', 356502, 'E', 1397421, '539600', '3937950', 130, 230, 330, 0, 'ALERT004', 'PREV004', '01:10:00', '3111', 4, '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('HJA005', 5, 'N', 356402, 'E', 1397321, '539550', '3937900', 90, 190, 290, 0, 'ALERT005', 'PREV005', '00:45:00', '4111', 5, '2025-06-23 02:12:44', '2025-06-23 02:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `lips_permited_approach_info`
--

CREATE TABLE `lips_permited_approach_info` (
  `id` varchar(100) NOT NULL,
  `area_element_num` bigint(20) DEFAULT NULL,
  `work_id` bigint(20) DEFAULT NULL,
  `area_info` varchar(1100) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_permited_approach_info`
--

INSERT INTO `lips_permited_approach_info` (`id`, `area_element_num`, `work_id`, `area_info`, `created_at`, `updated_at`) VALUES
('PA001', 1, 1, '{\"type\":\"Polygon\",\"coordinates\":[[[139.7671,35.6812],[139.7701,35.6832],[139.7721,35.6802],[139.7691,35.6782],[139.7671,35.6812]]]}', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PA002', 2, 2, '{\"type\":\"Polygon\",\"coordinates\":[[[139.7571,35.6712],[139.7601,35.6732],[139.7621,35.6702],[139.7591,35.6682],[139.7571,35.6712]]]}', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PA003', 3, 3, '{\"type\":\"Polygon\",\"coordinates\":[[[139.7471,35.6612],[139.7501,35.6632],[139.7521,35.6602],[139.7491,35.6582],[139.7471,35.6612]]]}', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PA004', 4, 4, '{\"type\":\"Polygon\",\"coordinates\":[[[139.7371,35.6512],[139.7401,35.6532],[139.7421,35.6502],[139.7391,35.6482],[139.7371,35.6512]]]}', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PA005', 5, 5, '{\"type\":\"Polygon\",\"coordinates\":[[[139.7271,35.6412],[139.7301,35.6432],[139.7321,35.6402],[139.7291,35.6382],[139.7271,35.6412]]]}', '2025-06-23 02:12:44', '2025-06-23 02:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `lips_prohibited_approach_info`
--

CREATE TABLE `lips_prohibited_approach_info` (
  `id` varchar(100) NOT NULL,
  `latitude` double DEFAULT NULL,
  `longitude` double DEFAULT NULL,
  `x_vector` varchar(100) DEFAULT NULL,
  `y_vector` varchar(100) DEFAULT NULL,
  `base_area` varchar(200) DEFAULT NULL,
  `extraction_area` varchar(100) DEFAULT NULL,
  `map_code` varchar(100) DEFAULT NULL,
  `prefectures` varchar(100) DEFAULT NULL,
  `municipalities` varchar(100) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_prohibited_approach_info`
--

INSERT INTO `lips_prohibited_approach_info` (`id`, `latitude`, `longitude`, `x_vector`, `y_vector`, `base_area`, `extraction_area`, `map_code`, `prefectures`, `municipalities`, `created_at`, `updated_at`) VALUES
('PRA001', 35.6812, 139.7671, '10', '20', 'Tokyo Area 1', 'Station', 'MAP001', 'Tokyo', 'Chiyoda', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PRA002', 35.6712, 139.7571, '15', '25', 'Tokyo Area 2', 'Park', 'MAP002', 'Tokyo', 'Shinjuku', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PRA003', 35.6612, 139.7471, '12', '22', 'Tokyo Area 3', 'Hospital', 'MAP003', 'Tokyo', 'Shibuya', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PRA004', 35.6512, 139.7371, '18', '28', 'Tokyo Area 4', 'School', 'MAP004', 'Tokyo', 'Minato', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('PRA005', 35.6412, 139.7271, '14', '24', 'Tokyo Area 5', 'Office', 'MAP005', 'Tokyo', 'Ota', '2025-06-23 02:12:44', '2025-06-23 02:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `lips_settings_info`
--

CREATE TABLE `lips_settings_info` (
  `id` varchar(100) NOT NULL,
  `key_name` varchar(100) DEFAULT NULL,
  `value` varchar(100) DEFAULT NULL,
  `summary` varchar(100) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_settings_info`
--

INSERT INTO `lips_settings_info` (`id`, `key_name`, `value`, `summary`, `created_at`, `updated_at`) VALUES
('SI001', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('SI002', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('SI003', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('SI004', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('SI005', 'language', 'ja', 'UI language', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER001_alert_sound', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER001_alert_vibration', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER001_estimationSec', 'estimationSec', '33', '接近予測時間 [秒]', '2025-06-23 02:12:43', '2025-06-23 02:24:50'),
('USER001_language', 'language', 'ja', 'UI language', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER001_map_zoom_level', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER001_neighborhoodThreshold', 'neighborhoodThreshold', '5000', '接近検知距離の閾値 [mm]', '2025-06-23 02:12:43', '2025-06-23 02:24:50'),
('USER001_refresh_interval', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_alert_sound', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_alert_vibration', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_estimationSec', 'estimationSec', '30', '接近予測時間 [秒]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_language', 'language', 'ja', 'UI language', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_map_zoom_level', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_neighborhoodThreshold', 'neighborhoodThreshold', '5000', '接近検知距離の閾値 [mm]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002_refresh_interval', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_alert_sound', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_alert_vibration', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_estimationSec', 'estimationSec', '30', '接近予測時間 [秒]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_language', 'language', 'ja', 'UI language', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_map_zoom_level', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_neighborhoodThreshold', 'neighborhoodThreshold', '5000', '接近検知距離の閾値 [mm]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003_refresh_interval', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_alert_sound', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_alert_vibration', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_estimationSec', 'estimationSec', '30', '接近予測時間 [秒]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_language', 'language', 'ja', 'UI language', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_map_zoom_level', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_neighborhoodThreshold', 'neighborhoodThreshold', '5000', '接近検知距離の閾値 [mm]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004_refresh_interval', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER005_alert_sound', 'alert_sound', 'enable', 'Enable alert sounds', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('USER005_alert_vibration', 'alert_vibration', 'enable', 'Enable alert vibration', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('USER005_estimationSec', 'estimationSec', '30', '接近予測時間 [秒]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER005_language', 'language', 'ja', 'UI language', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('USER005_map_zoom_level', 'map_zoom_level', '15', 'Default map zoom level', '2025-06-23 02:12:44', '2025-06-23 02:12:44'),
('USER005_neighborhoodThreshold', 'neighborhoodThreshold', '5000', '接近検知距離の閾値 [mm]', '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER005_refresh_interval', 'refresh_interval', '300', 'UI refresh interval in seconds', '2025-06-23 02:12:44', '2025-06-23 02:12:44');

-- --------------------------------------------------------

--
-- Table structure for table `lips_user_masters`
--

CREATE TABLE `lips_user_masters` (
  `id` varchar(100) NOT NULL,
  `user_id` int(20) NOT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_user_masters`
--

INSERT INTO `lips_user_masters` (`id`, `user_id`, `created_at`, `updated_at`) VALUES
('USER001', 13, '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER002', 12, '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER003', 11, '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER004', 8, '2025-06-23 02:12:43', '2025-06-23 02:12:43'),
('USER005', 9, '2025-06-23 02:12:43', '2025-06-23 02:12:43');

-- --------------------------------------------------------

--
-- Table structure for table `lips_work_info`
--

CREATE TABLE `lips_work_info` (
  `id` bigint(20) NOT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `work_name` varchar(100) DEFAULT NULL,
  `group_num` varchar(100) DEFAULT NULL,
  `created_at` varchar(100) DEFAULT NULL,
  `updated_at` varchar(100) DEFAULT NULL,
  `time_elapsed` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `lips_work_info`
--

INSERT INTO `lips_work_info` (`id`, `user_id`, `work_name`, `group_num`, `created_at`, `updated_at`, `time_elapsed`) VALUES
(1, 'USER001', 'SVLR0008', '1111', '2025-06-23 02:12:43', '2025-06-23 02:12:43', NULL),
(2, 'USER002', 'SVLR0010', '1112', '2025-06-23 02:12:43', '2025-06-23 02:12:43', NULL),
(3, 'USER003', 'SVLR0012', '2111', '2025-06-23 02:12:43', '2025-06-23 02:12:43', NULL),
(4, 'USER001', 'SVLR0015', '3111', '2025-06-23 02:12:43', '2025-06-23 02:12:43', NULL),
(5, 'USER002', 'SVLR0018', '4111', '2025-06-23 02:12:43', '2025-06-23 02:12:43', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `usermanagement_role`
--

CREATE TABLE `usermanagement_role` (
  `id` bigint(20) NOT NULL,
  `name` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `usermanagement_role`
--

INSERT INTO `usermanagement_role` (`id`, `name`) VALUES
(1, 'admin'),
(2, 'manager'),
(3, 'user');

-- --------------------------------------------------------

--
-- Table structure for table `usermanagement_user`
--

CREATE TABLE `usermanagement_user` (
  `id` bigint(20) NOT NULL,
  `password` varchar(128) NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) NOT NULL,
  `first_name` varchar(150) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  `email` varchar(254) NOT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `notes` longtext DEFAULT NULL,
  `otp` varchar(6) DEFAULT NULL,
  `otp_expired_at` datetime(6) DEFAULT NULL,
  `role_id` bigint(20) DEFAULT NULL,
  `company_name` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `usermanagement_user`
--

INSERT INTO `usermanagement_user` (`id`, `password`, `last_login`, `is_superuser`, `username`, `first_name`, `last_name`, `is_staff`, `is_active`, `date_joined`, `email`, `full_name`, `status`, `phone_number`, `notes`, `otp`, `otp_expired_at`, `role_id`, `company_name`) VALUES
(1, 'pbkdf2_sha256$600000$iHRW44gvKds6uJ3Z4QGBO2$8MyeyoEAfbigVXrPX3qP43qCs2gnKveuDC6fThXx+/0=', NULL, 0, 'admin', '', '', 0, 1, '2025-06-23 02:12:43.793535', '<EMAIL>', 'Admin User', 'active', NULL, NULL, NULL, NULL, 1, ''),
(5, 'pbkdf2_sha256$600000$c0Jrpbwp2KLqWLpXUWnvxv$RHNF8Z7y29t0YiMO/hnM5DWWIFOq9pdG0BnEkSrN/ls=', NULL, 0, 'user1', '', '', 0, 1, '2025-06-23 02:12:46.179510', '<EMAIL>', 'User One', 'active', '************', NULL, NULL, NULL, 3, 'Notification Alert User'),
(8, 'pbkdf2_sha256$600000$fEIF6HCxFBjlD2zRO2xev3$m8hK9oh++nq5RBXsNei1eoKi98xGE1rTffyT0hL0WC8=', NULL, 0, 'user', '', '', 0, 1, '2025-06-23 02:12:46.179510', '<EMAIL>', 'user', 'active', '018190734334', 'dd', NULL, NULL, 3, 'itage'),
(9, 'pbkdf2_sha256$600000$gx5LA2FQ7FGF9gK778MbQc$075uyVI5g5SQtHvTDSBDPFFFn8qRDYVFaLHfLeYuP7Y=', NULL, 0, 'manager', '', '', 0, 1, '2025-06-23 02:12:46.191701', '<EMAIL>', 'manager', 'active', '018190734334', NULL, NULL, NULL, 2, 'itage'),
(11, 'pbkdf2_sha256$600000$N7wSSYj0YIqfN6qxxjzAE4$JJ0+rfcNsWQrbQZXHehzu6oLNEN22FxGFX3wsNVlLZE=', NULL, 0, 'user123', '', '', 0, 1, '2025-06-23 02:12:46.196454', '<EMAIL>', 'user123', 'active', '018190734334', NULL, NULL, NULL, 3, 'itage'),
(12, 'pbkdf2_sha256$600000$U1hIEpiS6TVmwarObKAWjg$CaaO4QI+3byBumEujcx1ZWmfFHPBUMjJty4iM2tLa78=', NULL, 0, 'manager1', '', '', 0, 1, '2025-06-23 02:12:46.201937', '<EMAIL>', 'manager', 'active', '01822223933', NULL, NULL, NULL, 2, 'itage'),
(13, 'pbkdf2_sha256$600000$GjhtjIe.lyqKcHuX15W08A$HjwxBAgQItuMhyNUHz4/hVAp/zTSTYsCWDHY1OWvVgg=', '2025-06-23 02:19:41.128963', 0, 'towhidcse4', '', '', 0, 1, '2025-06-23 02:19:08.153306', '<EMAIL>', 'towhi', 'active', '12345567', 'jp', NULL, NULL, 1, 'eits');

-- --------------------------------------------------------

--
-- Table structure for table `usermanagement_user_groups`
--

CREATE TABLE `usermanagement_user_groups` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `group_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `usermanagement_user_user_permissions`
--

CREATE TABLE `usermanagement_user_user_permissions` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `auth_group`
--
ALTER TABLE `auth_group`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `auth_group_permissions`
--
ALTER TABLE `auth_group_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  ADD KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`);

--
-- Indexes for table `auth_permission`
--
ALTER TABLE `auth_permission`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`);

--
-- Indexes for table `django_admin_log`
--
ALTER TABLE `django_admin_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  ADD KEY `django_admin_log_user_id_c564eba6_fk_usermanagement_user_id` (`user_id`);

--
-- Indexes for table `django_content_type`
--
ALTER TABLE `django_content_type`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`);

--
-- Indexes for table `django_migrations`
--
ALTER TABLE `django_migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `django_session`
--
ALTER TABLE `django_session`
  ADD PRIMARY KEY (`session_key`),
  ADD KEY `django_session_expire_date_a5c62663` (`expire_date`);

--
-- Indexes for table `lips_active_work`
--
ALTER TABLE `lips_active_work`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_alert_instruction_management_info`
--
ALTER TABLE `lips_alert_instruction_management_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_business_operator_masters`
--
ALTER TABLE `lips_business_operator_masters`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `domain_name` (`domain_name`);

--
-- Indexes for table `lips_device_masters`
--
ALTER TABLE `lips_device_masters`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `lips_device_masters_work_id_8ba19658_fk` (`work_id`);

--
-- Indexes for table `lips_device_receive_data`
--
ALTER TABLE `lips_device_receive_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lips_device_receive_data_device_id_36a4843c_fk` (`device_id`);

--
-- Indexes for table `lips_fix_info`
--
ALTER TABLE `lips_fix_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_historical_judge_alert_instruction`
--
ALTER TABLE `lips_historical_judge_alert_instruction`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lips_historical_judge_alert_instruction_device_id_002aebe2_fk` (`device_id`);

--
-- Indexes for table `lips_permited_approach_info`
--
ALTER TABLE `lips_permited_approach_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `lips_permited_approach_info_work_id_92781458_fk` (`work_id`);

--
-- Indexes for table `lips_prohibited_approach_info`
--
ALTER TABLE `lips_prohibited_approach_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_settings_info`
--
ALTER TABLE `lips_settings_info`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_user_masters`
--
ALTER TABLE `lips_user_masters`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `lips_work_info`
--
ALTER TABLE `lips_work_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `usermanagement_role`
--
ALTER TABLE `usermanagement_role`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `usermanagement_user`
--
ALTER TABLE `usermanagement_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `usermanagement_user_role_id_fce7f8e1_fk_usermanagement_role_id` (`role_id`);

--
-- Indexes for table `usermanagement_user_groups`
--
ALTER TABLE `usermanagement_user_groups`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `usermanagement_user_groups_user_id_group_id_d79ad12f_uniq` (`user_id`,`group_id`),
  ADD KEY `usermanagement_user_groups_group_id_8a5e23fc_fk_auth_group_id` (`group_id`);

--
-- Indexes for table `usermanagement_user_user_permissions`
--
ALTER TABLE `usermanagement_user_user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `usermanagement_user_user_user_id_permission_id_b36c426b_uniq` (`user_id`,`permission_id`),
  ADD KEY `usermanagement_user__permission_id_94e34351_fk_auth_perm` (`permission_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `auth_group`
--
ALTER TABLE `auth_group`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `auth_group_permissions`
--
ALTER TABLE `auth_group_permissions`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `auth_permission`
--
ALTER TABLE `auth_permission`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=77;

--
-- AUTO_INCREMENT for table `django_admin_log`
--
ALTER TABLE `django_admin_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `django_content_type`
--
ALTER TABLE `django_content_type`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `django_migrations`
--
ALTER TABLE `django_migrations`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `lips_active_work`
--
ALTER TABLE `lips_active_work`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `lips_device_masters`
--
ALTER TABLE `lips_device_masters`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `lips_work_info`
--
ALTER TABLE `lips_work_info`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `usermanagement_role`
--
ALTER TABLE `usermanagement_role`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `usermanagement_user`
--
ALTER TABLE `usermanagement_user`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `usermanagement_user_groups`
--
ALTER TABLE `usermanagement_user_groups`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `usermanagement_user_user_permissions`
--
ALTER TABLE `usermanagement_user_user_permissions`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `auth_group_permissions`
--
ALTER TABLE `auth_group_permissions`
  ADD CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  ADD CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`);

--
-- Constraints for table `auth_permission`
--
ALTER TABLE `auth_permission`
  ADD CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`);

--
-- Constraints for table `django_admin_log`
--
ALTER TABLE `django_admin_log`
  ADD CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  ADD CONSTRAINT `django_admin_log_user_id_c564eba6_fk_usermanagement_user_id` FOREIGN KEY (`user_id`) REFERENCES `usermanagement_user` (`id`);

--
-- Constraints for table `lips_device_masters`
--
ALTER TABLE `lips_device_masters`
  ADD CONSTRAINT `lips_device_masters_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `lips_user_masters` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `lips_device_masters_work_id_8ba19658_fk` FOREIGN KEY (`work_id`) REFERENCES `lips_work_info` (`id`);

--
-- Constraints for table `lips_device_receive_data`
--
ALTER TABLE `lips_device_receive_data`
  ADD CONSTRAINT `lips_device_receive_data_device_id_36a4843c_fk` FOREIGN KEY (`device_id`) REFERENCES `lips_device_masters` (`id`);

--
-- Constraints for table `lips_historical_judge_alert_instruction`
--
ALTER TABLE `lips_historical_judge_alert_instruction`
  ADD CONSTRAINT `lips_historical_judge_alert_instruction_device_id_002aebe2_fk` FOREIGN KEY (`device_id`) REFERENCES `lips_device_masters` (`id`);

--
-- Constraints for table `lips_permited_approach_info`
--
ALTER TABLE `lips_permited_approach_info`
  ADD CONSTRAINT `lips_permited_approach_info_work_id_92781458_fk` FOREIGN KEY (`work_id`) REFERENCES `lips_work_info` (`id`);

--
-- Constraints for table `lips_work_info`
--
ALTER TABLE `lips_work_info`
  ADD CONSTRAINT `lips_work_info_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `lips_user_masters` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `usermanagement_user`
--
ALTER TABLE `usermanagement_user`
  ADD CONSTRAINT `usermanagement_user_role_id_fce7f8e1_fk_usermanagement_role_id` FOREIGN KEY (`role_id`) REFERENCES `usermanagement_role` (`id`);

--
-- Constraints for table `usermanagement_user_groups`
--
ALTER TABLE `usermanagement_user_groups`
  ADD CONSTRAINT `usermanagement_user__user_id_681d81f7_fk_usermanag` FOREIGN KEY (`user_id`) REFERENCES `usermanagement_user` (`id`),
  ADD CONSTRAINT `usermanagement_user_groups_group_id_8a5e23fc_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`);

--
-- Constraints for table `usermanagement_user_user_permissions`
--
ALTER TABLE `usermanagement_user_user_permissions`
  ADD CONSTRAINT `usermanagement_user__permission_id_94e34351_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  ADD CONSTRAINT `usermanagement_user__user_id_c5c2e762_fk_usermanag` FOREIGN KEY (`user_id`) REFERENCES `usermanagement_user` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
