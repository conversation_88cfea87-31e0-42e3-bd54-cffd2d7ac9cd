#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to populate the database with initial settings data.
This script will create settings records if they don't exist,
and will handle duplicate key errors gracefully.
"""

import os
import sys
import django
from django.utils import timezone
from django.db import IntegrityError

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from lips.models import SettingsInfo, UserMaster

def create_settings_for_user(user_id, settings_data):
    """
    Create settings for a user with proper error handling.

    Args:
        user_id (str): The user ID
        settings_data (list): List of dictionaries with key_name and value
    """
    print(f"Creating settings for user {user_id}...")

    for setting in settings_data:
        key_name = setting['key_name']
        value = setting['value']
        summary = setting.get('summary', '')

        # Create a unique ID for each setting
        setting_id = f"{user_id}_{key_name}"

        try:
            # First, check if a setting with either ID exists
            setting_obj = SettingsInfo.objects.filter(
                key_name=key_name
            ).filter(
                id__in=[user_id, setting_id]
            ).first()

            if setting_obj:
                # Update existing setting
                setting_obj.value = value
                if summary:
                    setting_obj.summary = summary
                setting_obj.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                setting_obj.save()
                print(f"  Updated setting: {setting_obj.id} = {value}")
            else:
                # Try to create with the unique ID first (safer)
                try:
                    SettingsInfo.objects.create(
                        id=setting_id,
                        key_name=key_name,
                        value=value,
                        summary=summary,
                        created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                        updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                    )
                    print(f"  Created setting: {setting_id} = {value}")
                except IntegrityError:
                    # If that fails, try with the user_id
                    try:
                        SettingsInfo.objects.create(
                            id=user_id,
                            key_name=key_name,
                            value=value,
                            summary=summary,
                            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                        )
                        print(f"  Created setting: {user_id} = {value}")
                    except IntegrityError as e:
                        print(f"  Error creating setting {key_name}: {e}")
        except Exception as e:
            print(f"  Unexpected error creating setting {key_name}: {e}")

def populate_default_settings():
    """Populate default settings for all users"""
    # Get all users
    users = UserMaster.objects.all()

    if not users:
        print("No users found. Creating a default user...")
        default_user = UserMaster.objects.create(
            id="USER001",
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        users = [default_user]

    # Default settings for each user
    default_settings = [
        {'key_name': 'neighborhoodThreshold', 'value': '5000', 'summary': '接近検知距離の閾値 [mm]'},
        {'key_name': 'estimationSec', 'value': '30', 'summary': '接近予測時間 [秒]'},
        {'key_name': 'refresh_interval', 'value': '300', 'summary': 'UI refresh interval in seconds'},
        {'key_name': 'map_zoom_level', 'value': '15', 'summary': 'Default map zoom level'},
        {'key_name': 'alert_sound', 'value': 'enable', 'summary': 'Enable alert sounds'},
        {'key_name': 'alert_vibration', 'value': 'enable', 'summary': 'Enable alert vibration'},
        {'key_name': 'language', 'value': 'ja', 'summary': 'UI language'},
    ]

    # Create settings for each user
    for user in users:
        create_settings_for_user(user.id, default_settings)

if __name__ == "__main__":
    print("Starting to populate default settings...")
    populate_default_settings()
    print("Finished populating default settings.")
