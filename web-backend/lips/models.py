from django.db import models


class AlertInstructionManagementInfo(models.Model):
    """Model for alert instruction management information"""
    id = models.IntegerField(primary_key=True)

    class Meta:
        db_table = 'lips_alert_instruction_management_info'

    def __str__(self):
        return f"Alert Instruction Info {self.id}"


class UserMaster(models.Model):
    """Model for user master information"""
    id = models.CharField(max_length=100, primary_key=True)
    user_id = models.IntegerField(null=True, blank=True, help_text="Reference to User ID from usermanagement app")
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_user_masters'

    def __str__(self):
        return f"User Master: {self.id} (User ID: {self.user_id})"


class WorkInfo(models.Model):
    """Model for work information"""
    # Django will automatically add an auto-incrementing primary key field named 'id'
    user_id = models.ForeignKey('UserMaster', on_delete=models.SET_NULL, null=True, blank=True, db_column='user_id')
    work_name = models.CharField(max_length=100, null=True, blank=True)
    group_num = models.CharField(max_length=100, null=True, blank=True)
    time_elapsed = models.CharField(max_length=50, null=True, blank=True)  # Added for active work duration
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_work_info'

    def __str__(self):
        return f"Work Info: {self.work_name} ({self.id})"


class DeviceMaster(models.Model):
    """Model for device master information"""
    DEVICE_STATUS={
  "Server Awaiting Response": "サーバ応答待ち",
  "Area Change": "エリア変更",
  "No Entry Entering Area": "進入禁止エリア進入",
  "No Alert": "アラート無し",
  "Location Information Unstable": "位置情報不安定",
  "No Entry Approaching Area": "進入禁止エリア接近"
}
    # Django will automatically add an auto-incrementing primary key field named 'id'
    user_id = models.ForeignKey('UserMaster', on_delete=models.SET_NULL, null=True, blank=True, db_column='user_id')
    display_device_id = models.CharField(max_length=100, null=True, blank=True)
    device_name = models.CharField(max_length=100, null=True, blank=True)
    work_id = models.ForeignKey('WorkInfo', on_delete=models.SET_NULL, null=True, blank=True, db_column='work_id')
    work_time = models.CharField(max_length=100, null=True, blank=True)
    battery = models.BigIntegerField(null=True, blank=True)
    previous_alert_instruction = models.CharField(max_length=100, null=True, blank=True)
    status=models.CharField(max_length=100, null=True, blank=True, choices=DEVICE_STATUS.items())
    signal_period = models.BigIntegerField(null=True, blank=True)
    approach_area_distance = models.FloatField(null=True, blank=True, help_text="Approach area distance in meters")
    approach_area_seconds = models.IntegerField(null=True, blank=True, help_text="Approach area time in seconds")
    # Work approval fields
    work_approval_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected')
        ],
        default='pending',
        help_text="Work approval status"
    )
    approval_message = models.TextField(null=True, blank=True, help_text="Approval/rejection message")
    approved_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when work was approved/rejected")
    created_at = models.DateTimeField(null=True, blank=True, auto_now_add=True)
    updated_at = models.DateTimeField(null=True, blank=True, auto_now=True)

    class Meta:
        db_table = 'lips_device_masters'

    def __str__(self):
        return f"Device Master: {self.device_name} ({self.id})"


class DeviceReceiveData(models.Model):
    """Model for device receive data"""
    id = models.CharField(max_length=100, primary_key=True)
    device_id = models.ForeignKey('DeviceMaster', on_delete=models.SET_NULL, null=True, blank=True, db_column='device_id')
    time = models.CharField(max_length=100, null=True, blank=True)
    previous_alert_id = models.CharField(max_length=100, null=True, blank=True)
    battery = models.BigIntegerField(null=True, blank=True)
    dop = models.BigIntegerField(null=True, blank=True)
    ns_latitude_identifier = models.CharField(max_length=100, null=True, blank=True)
    latitude = models.BigIntegerField(null=True, blank=True)
    ew_longitude_identifier = models.CharField(max_length=100, null=True, blank=True)
    longitude = models.BigIntegerField(null=True, blank=True)
    x_acceleration = models.BigIntegerField(null=True, blank=True)
    y_acceleration = models.BigIntegerField(null=True, blank=True)
    z_acceleration = models.BigIntegerField(null=True, blank=True)
    alive_count = models.BigIntegerField(null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_device_receive_data'

    def __str__(self):
        return f"Device Receive Data: {self.id}"


class FixInfo(models.Model):
    """Model for fix information"""
    id = models.CharField(max_length=100, primary_key=True)
    key_name = models.CharField(max_length=100, null=True, blank=True)
    val = models.CharField(max_length=100, null=True, blank=True)
    summary = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_fix_info'

    def __str__(self):
        return f"Fix Info: {self.key_name} ({self.id})"


class HistoricalJudgeAlertInstruction(models.Model):
    """Model for historical judge alert instruction"""
    id = models.CharField(max_length=100, primary_key=True)
    device_id = models.ForeignKey('DeviceMaster', on_delete=models.SET_NULL, null=True, blank=True, db_column='device_id')
    ns_latitude_identifier = models.CharField(max_length=100, null=True, blank=True)
    latitude = models.BigIntegerField(null=True, blank=True)
    ew_longitude_identifier = models.CharField(max_length=100, null=True, blank=True)
    longitude = models.BigIntegerField(null=True, blank=True)
    utm_x = models.CharField(max_length=100, null=True, blank=True)
    utm_y = models.CharField(max_length=100, null=True, blank=True)
    x_acceleration = models.BigIntegerField(null=True, blank=True)
    y_acceleration = models.BigIntegerField(null=True, blank=True)
    z_acceleration = models.BigIntegerField(null=True, blank=True)
    alert_instruction = models.BigIntegerField(null=True, blank=True)
    alert_id = models.CharField(max_length=100, null=True, blank=True)
    previous_alert_id = models.CharField(max_length=100, null=True, blank=True)
    work_time = models.CharField(max_length=100, null=True, blank=True)
    group_num = models.CharField(max_length=100, null=True, blank=True)
    alive_count = models.BigIntegerField(null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_historical_judge_alert_instruction'

    def __str__(self):
        return f"Historical Judge Alert Instruction: {self.id}"


class PermitedApproachInfo(models.Model):
    """Model for permitted approach information"""
    id = models.CharField(max_length=100, primary_key=True)
    area_element_num = models.BigIntegerField(null=True, blank=True)
    work_id = models.ForeignKey('WorkInfo', on_delete=models.SET_NULL, null=True, blank=True, db_column='work_id')
    area_info = models.CharField(max_length=1100, null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_permited_approach_info'

    def __str__(self):
        return f"Permitted Approach Info: {self.id}"


class ProhibitedApproachInfo(models.Model):
    """Model for prohibited approach information"""
    id = models.CharField(max_length=100, primary_key=True)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    x_vector = models.CharField(max_length=100, null=True, blank=True)
    y_vector = models.CharField(max_length=100, null=True, blank=True)
    base_area = models.CharField(max_length=200, null=True, blank=True)
    extraction_area = models.CharField(max_length=100, null=True, blank=True)
    map_code = models.CharField(max_length=100, null=True, blank=True)
    prefectures = models.CharField(max_length=100, null=True, blank=True)
    municipalities = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_prohibited_approach_info'

    def __str__(self):
        return f"Prohibited Approach Info: {self.id}"


class ActiveWork(models.Model):
    work_name = models.CharField(max_length=255)
    group_number = models.CharField(max_length=100, null=True, blank=True)
    assigned_devices = models.IntegerField(default=0)
    time_elapsed = models.CharField(max_length=50, null=True, blank=True)  # Consider models.DurationField if appropriate
    accessible_areas = models.IntegerField(default=0)
    # work_info = models.ForeignKey(WorkInfo, on_delete=models.CASCADE, null=True, blank=True) # Optional: if it relates to an existing WorkInfo
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'lips_active_work'
        verbose_name = 'Active Work'
        verbose_name_plural = 'Active Works'

    def __str__(self):
        return f"Active Work: {self.work_name} - {self.group_number or self.id}"


class SettingsInfo(models.Model):
    """Model for settings information"""
    id = models.CharField(max_length=100, primary_key=True)
    key_name = models.CharField(max_length=100, null=True, blank=True)
    value = models.CharField(max_length=100, null=True, blank=True)
    summary = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.CharField(max_length=100, null=True, blank=True)
    updated_at = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        db_table = 'lips_settings_info'

    def __str__(self):
        return f"Settings Info: {self.key_name} ({self.id})"
