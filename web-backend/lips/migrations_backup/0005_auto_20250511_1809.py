# Generated by Django 4.2.20 on 2025-05-11 18:09
# This migration has been disabled because it was causing issues
# It was dropping the entire database which is not a good practice in migrations

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0004_recreate_tables"),
    ]

    operations = [
        # Original operation removed because it was dropping the entire database
        # migrations.RunSQL(
        #     """
        #     DROP DATABASE IF EXISTS lipsweb;
        #     CREATE DATABASE lipsweb;
        #     USE lipsweb;
        #     """,
        #     reverse_sql="""
        #     -- No reverse SQL needed
        #     """
        # ),
    ]
