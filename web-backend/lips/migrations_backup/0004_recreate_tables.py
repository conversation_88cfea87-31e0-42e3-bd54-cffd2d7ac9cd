from django.db import migrations, models
import uuid

class Migration(migrations.Migration):

    dependencies = [
        ('lips', '0003_merge_20250511_1802'),
    ]

    operations = [
        # Create new tables with auto-incrementing IDs
        migrations.RunSQL(
            """
            CREATE TABLE lips_work_info_new (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(100) NULL,
                work_name VARCHAR(100) NULL,
                group_num VARCHAR(100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (user_id) REFERENCES lips_user_masters(id) ON DELETE SET NULL
            );
            """,
            "DROP TABLE IF EXISTS lips_work_info_new;"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE lips_device_masters_new (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(100) NULL,
                display_device_id VARCHAR(100) NULL,
                device_name VARCHAR(100) NULL,
                work_id INT NULL,
                work_time VARCHAR(100) NULL,
                battery BIGINT NULL,
                previous_alert_instruction VARCHAR(100) NULL,
                signal_period BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (user_id) REFERENCES lips_user_masters(id) ON DELETE SET NULL,
                FOREIGN KEY (work_id) REFERENCES lips_work_info_new(id) ON DELETE SET NULL
            );
            """,
            "DROP TABLE IF EXISTS lips_device_masters_new;"
        ),
        
        # Copy data from old tables to new tables
        migrations.RunSQL(
            """
            INSERT INTO lips_work_info_new (user_id, work_name, group_num, created_at, updated_at)
            SELECT user_id, work_name, group_num, created_at, updated_at
            FROM lips_work_info;
            """,
            "DELETE FROM lips_work_info_new;"
        ),
        migrations.RunSQL(
            """
            INSERT INTO lips_device_masters_new (user_id, display_device_id, device_name, work_time, battery, previous_alert_instruction, signal_period, created_at, updated_at)
            SELECT user_id, display_device_id, device_name, work_time, battery, previous_alert_instruction, signal_period, created_at, updated_at
            FROM lips_device_masters;
            """,
            "DELETE FROM lips_device_masters_new;"
        ),
        
        # Update work_id in device_masters_new based on work_name
        migrations.RunSQL(
            """
            UPDATE lips_device_masters_new dm
            JOIN lips_device_masters old_dm ON dm.display_device_id = old_dm.display_device_id
            JOIN lips_work_info old_wi ON old_dm.work_id = old_wi.id
            JOIN lips_work_info_new new_wi ON old_wi.work_name = new_wi.work_name
            SET dm.work_id = new_wi.id
            WHERE old_dm.work_id IS NOT NULL;
            """,
            ""
        ),
        
        # Update foreign keys in other tables
        migrations.RunSQL(
            """
            CREATE TABLE lips_permited_approach_info_new (
                id VARCHAR(100) PRIMARY KEY,
                area_element_num BIGINT NULL,
                work_id INT NULL,
                area_info VARCHAR(1100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (work_id) REFERENCES lips_work_info_new(id) ON DELETE SET NULL
            );
            """,
            "DROP TABLE IF EXISTS lips_permited_approach_info_new;"
        ),
        migrations.RunSQL(
            """
            INSERT INTO lips_permited_approach_info_new (id, area_element_num, area_info, created_at, updated_at)
            SELECT id, area_element_num, area_info, created_at, updated_at
            FROM lips_permited_approach_info;
            """,
            "DELETE FROM lips_permited_approach_info_new;"
        ),
        migrations.RunSQL(
            """
            UPDATE lips_permited_approach_info_new pai
            JOIN lips_permited_approach_info old_pai ON pai.id = old_pai.id
            JOIN lips_work_info old_wi ON old_pai.work_id = old_wi.id
            JOIN lips_work_info_new new_wi ON old_wi.work_name = new_wi.work_name
            SET pai.work_id = new_wi.id
            WHERE old_pai.work_id IS NOT NULL;
            """,
            ""
        ),
        
        # Create new tables for device-related foreign keys
        migrations.RunSQL(
            """
            CREATE TABLE lips_device_receive_data_new (
                id VARCHAR(100) PRIMARY KEY,
                device_id INT NULL,
                time VARCHAR(100) NULL,
                previous_alert_id VARCHAR(100) NULL,
                battery BIGINT NULL,
                dop BIGINT NULL,
                ns_latitude_identifier VARCHAR(100) NULL,
                latitude BIGINT NULL,
                ew_longitude_identifier VARCHAR(100) NULL,
                longitude BIGINT NULL,
                x_acceleration BIGINT NULL,
                y_acceleration BIGINT NULL,
                z_acceleration BIGINT NULL,
                alive_count BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (device_id) REFERENCES lips_device_masters_new(id) ON DELETE SET NULL
            );
            """,
            "DROP TABLE IF EXISTS lips_device_receive_data_new;"
        ),
        migrations.RunSQL(
            """
            INSERT INTO lips_device_receive_data_new (id, time, previous_alert_id, battery, dop, ns_latitude_identifier, latitude, ew_longitude_identifier, longitude, x_acceleration, y_acceleration, z_acceleration, alive_count, created_at, updated_at)
            SELECT id, time, previous_alert_id, battery, dop, ns_latitude_identifier, latitude, ew_longitude_identifier, longitude, x_acceleration, y_acceleration, z_acceleration, alive_count, created_at, updated_at
            FROM lips_device_receive_data;
            """,
            "DELETE FROM lips_device_receive_data_new;"
        ),
        migrations.RunSQL(
            """
            UPDATE lips_device_receive_data_new drd
            JOIN lips_device_receive_data old_drd ON drd.id = old_drd.id
            JOIN lips_device_masters old_dm ON old_drd.device_id = old_dm.id
            JOIN lips_device_masters_new new_dm ON old_dm.display_device_id = new_dm.display_device_id
            SET drd.device_id = new_dm.id
            WHERE old_drd.device_id IS NOT NULL;
            """,
            ""
        ),
        
        # Create new historical judge alert instruction table
        migrations.RunSQL(
            """
            CREATE TABLE lips_historical_judge_alert_instruction_new (
                id VARCHAR(100) PRIMARY KEY,
                device_id INT NULL,
                ns_latitude_identifier VARCHAR(100) NULL,
                latitude BIGINT NULL,
                ew_longitude_identifier VARCHAR(100) NULL,
                longitude BIGINT NULL,
                utm_x VARCHAR(100) NULL,
                utm_y VARCHAR(100) NULL,
                x_acceleration BIGINT NULL,
                y_acceleration BIGINT NULL,
                z_acceleration BIGINT NULL,
                alert_instruction BIGINT NULL,
                alert_id VARCHAR(100) NULL,
                previous_alert_id VARCHAR(100) NULL,
                work_time VARCHAR(100) NULL,
                group_num VARCHAR(100) NULL,
                alive_count BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (device_id) REFERENCES lips_device_masters_new(id) ON DELETE SET NULL
            );
            """,
            "DROP TABLE IF EXISTS lips_historical_judge_alert_instruction_new;"
        ),
        migrations.RunSQL(
            """
            INSERT INTO lips_historical_judge_alert_instruction_new (id, ns_latitude_identifier, latitude, ew_longitude_identifier, longitude, utm_x, utm_y, x_acceleration, y_acceleration, z_acceleration, alert_instruction, alert_id, previous_alert_id, work_time, group_num, alive_count, created_at, updated_at)
            SELECT id, ns_latitude_identifier, latitude, ew_longitude_identifier, longitude, utm_x, utm_y, x_acceleration, y_acceleration, z_acceleration, alert_instruction, alert_id, previous_alert_id, work_time, group_num, alive_count, created_at, updated_at
            FROM lips_historical_judge_alert_instruction;
            """,
            "DELETE FROM lips_historical_judge_alert_instruction_new;"
        ),
        migrations.RunSQL(
            """
            UPDATE lips_historical_judge_alert_instruction_new hjai
            JOIN lips_historical_judge_alert_instruction old_hjai ON hjai.id = old_hjai.id
            JOIN lips_device_masters old_dm ON old_hjai.device_id = old_dm.id
            JOIN lips_device_masters_new new_dm ON old_dm.display_device_id = new_dm.display_device_id
            SET hjai.device_id = new_dm.id
            WHERE old_hjai.device_id IS NOT NULL;
            """,
            ""
        ),
        
        # Rename tables
        migrations.RunSQL(
            """
            DROP TABLE lips_historical_judge_alert_instruction;
            DROP TABLE lips_device_receive_data;
            DROP TABLE lips_permited_approach_info;
            DROP TABLE lips_device_masters;
            DROP TABLE lips_work_info;
            
            RENAME TABLE lips_work_info_new TO lips_work_info;
            RENAME TABLE lips_device_masters_new TO lips_device_masters;
            RENAME TABLE lips_permited_approach_info_new TO lips_permited_approach_info;
            RENAME TABLE lips_device_receive_data_new TO lips_device_receive_data;
            RENAME TABLE lips_historical_judge_alert_instruction_new TO lips_historical_judge_alert_instruction;
            """,
            ""
        ),
    ]
