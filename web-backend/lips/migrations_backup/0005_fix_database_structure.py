# Generated manually to fix database structure

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0004_recreate_tables"),
    ]

    operations = [
        # Create all necessary tables if they don't exist
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_user_masters (
                id VARCHAR(100) PRIMARY KEY,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_work_info (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(100) NULL,
                work_name VARCHAR(100) NULL,
                group_num VARCHAR(100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (user_id) REFERENCES lips_user_masters(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_device_masters (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(100) NULL,
                display_device_id VARCHAR(100) NULL,
                device_name VARCHAR(100) NULL,
                work_id INT NULL,
                assigned_at DATETIME(6) NULL,
                work_time VARCHAR(100) NULL,
                battery BIGINT NULL,
                previous_alert_instruction VARCHAR(100) NULL,
                signal_period BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (user_id) REFERENCES lips_user_masters(id) ON DELETE SET NULL,
                FOREIGN KEY (work_id) REFERENCES lips_work_info(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_device_receive_data (
                id VARCHAR(100) PRIMARY KEY,
                device_id INT NULL,
                time VARCHAR(100) NULL,
                previous_alert_id VARCHAR(100) NULL,
                battery BIGINT NULL,
                dop BIGINT NULL,
                ns_latitude_identifier VARCHAR(100) NULL,
                latitude BIGINT NULL,
                ew_longitude_identifier VARCHAR(100) NULL,
                longitude BIGINT NULL,
                x_acceleration BIGINT NULL,
                y_acceleration BIGINT NULL,
                z_acceleration BIGINT NULL,
                alive_count BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (device_id) REFERENCES lips_device_masters(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_historical_judge_alert_instruction (
                id VARCHAR(100) PRIMARY KEY,
                device_id INT NULL,
                ns_latitude_identifier VARCHAR(100) NULL,
                latitude BIGINT NULL,
                ew_longitude_identifier VARCHAR(100) NULL,
                longitude BIGINT NULL,
                utm_x VARCHAR(100) NULL,
                utm_y VARCHAR(100) NULL,
                x_acceleration BIGINT NULL,
                y_acceleration BIGINT NULL,
                z_acceleration BIGINT NULL,
                alert_instruction BIGINT NULL,
                alert_id VARCHAR(100) NULL,
                previous_alert_id VARCHAR(100) NULL,
                work_time VARCHAR(100) NULL,
                group_num VARCHAR(100) NULL,
                alive_count BIGINT NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (device_id) REFERENCES lips_device_masters(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_permited_approach_info (
                id VARCHAR(100) PRIMARY KEY,
                area_element_num BIGINT NULL,
                work_id INT NULL,
                area_info VARCHAR(1100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL,
                FOREIGN KEY (work_id) REFERENCES lips_work_info(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_settings_info (
                id VARCHAR(100) PRIMARY KEY,
                key_name VARCHAR(100) NULL,
                value VARCHAR(100) NULL,
                summary VARCHAR(100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_fix_info (
                id VARCHAR(100) PRIMARY KEY,
                key_name VARCHAR(100) NULL,
                val VARCHAR(100) NULL,
                summary VARCHAR(100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_prohibited_approach_info (
                id VARCHAR(100) PRIMARY KEY,
                latitude FLOAT NULL,
                longitude FLOAT NULL,
                x_vector VARCHAR(100) NULL,
                y_vector VARCHAR(100) NULL,
                base_area VARCHAR(200) NULL,
                extraction_area VARCHAR(100) NULL,
                map_code VARCHAR(100) NULL,
                prefectures VARCHAR(100) NULL,
                municipalities VARCHAR(100) NULL,
                created_at VARCHAR(100) NULL,
                updated_at VARCHAR(100) NULL
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_alert_instruction_management_info (
                id INT PRIMARY KEY
            );
            """,
            reverse_sql="-- No reverse SQL needed"
        ),
    ]
