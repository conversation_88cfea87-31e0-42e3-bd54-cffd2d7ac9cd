from django.db import migrations, models
import uuid

def generate_temp_id_for_work(apps, schema_editor):
    """
    Generate a temporary integer ID for each work record
    """
    WorkInfo = apps.get_model('lips', 'WorkInfo')
    for i, work in enumerate(WorkInfo.objects.all()):
        # Store the original string ID in a temporary field
        work.temp_id = work.id
        # Assign a new integer ID
        work.id = i + 1
        work.save()

def generate_temp_id_for_device(apps, schema_editor):
    """
    Generate a temporary integer ID for each device record
    """
    DeviceMaster = apps.get_model('lips', 'DeviceMaster')
    for i, device in enumerate(DeviceMaster.objects.all()):
        # Store the original string ID in a temporary field
        device.temp_id = device.id
        # Assign a new integer ID
        device.id = i + 1
        device.save()

def update_foreign_keys_for_work(apps, schema_editor):
    """
    Update foreign keys that reference WorkInfo
    """
    WorkInfo = apps.get_model('lips', 'WorkInfo')
    DeviceMaster = apps.get_model('lips', 'DeviceMaster')
    PermitedApproachInfo = apps.get_model('lips', 'PermitedApproachInfo')
    
    # Create a mapping of old IDs to new IDs
    work_id_map = {work.temp_id: work.id for work in WorkInfo.objects.all()}
    
    # Update DeviceMaster foreign keys
    for device in DeviceMaster.objects.filter(work_id__isnull=False):
        if device.work_id_id in work_id_map:
            device.work_id_id = work_id_map[device.work_id_id]
            device.save()
    
    # Update PermitedApproachInfo foreign keys
    for approach in PermitedApproachInfo.objects.filter(work_id__isnull=False):
        if approach.work_id_id in work_id_map:
            approach.work_id_id = work_id_map[approach.work_id_id]
            approach.save()

def update_foreign_keys_for_device(apps, schema_editor):
    """
    Update foreign keys that reference DeviceMaster
    """
    DeviceMaster = apps.get_model('lips', 'DeviceMaster')
    DeviceReceiveData = apps.get_model('lips', 'DeviceReceiveData')
    HistoricalJudgeAlertInstruction = apps.get_model('lips', 'HistoricalJudgeAlertInstruction')
    
    # Create a mapping of old IDs to new IDs
    device_id_map = {device.temp_id: device.id for device in DeviceMaster.objects.all()}
    
    # Update DeviceReceiveData foreign keys
    for data in DeviceReceiveData.objects.filter(device_id__isnull=False):
        if data.device_id_id in device_id_map:
            data.device_id_id = device_id_map[data.device_id_id]
            data.save()
    
    # Update HistoricalJudgeAlertInstruction foreign keys
    for alert in HistoricalJudgeAlertInstruction.objects.filter(device_id__isnull=False):
        if alert.device_id_id in device_id_map:
            alert.device_id_id = device_id_map[alert.device_id_id]
            alert.save()

class Migration(migrations.Migration):

    dependencies = [
        ('lips', '0001_initial'),
    ]

    operations = [
        # Add temporary fields to store original string IDs
        migrations.AddField(
            model_name='workinfo',
            name='temp_id',
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='devicemaster',
            name='temp_id',
            field=models.CharField(max_length=100, null=True),
        ),
        
        # Generate temporary IDs
        migrations.RunPython(generate_temp_id_for_work),
        migrations.RunPython(generate_temp_id_for_device),
        
        # Update foreign keys
        migrations.RunPython(update_foreign_keys_for_work),
        migrations.RunPython(update_foreign_keys_for_device),
        
        # Change ID fields to AutoField
        migrations.AlterField(
            model_name='workinfo',
            name='id',
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='devicemaster',
            name='id',
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        
        # Remove temporary fields
        migrations.RemoveField(
            model_name='workinfo',
            name='temp_id',
        ),
        migrations.RemoveField(
            model_name='devicemaster',
            name='temp_id',
        ),
    ]
