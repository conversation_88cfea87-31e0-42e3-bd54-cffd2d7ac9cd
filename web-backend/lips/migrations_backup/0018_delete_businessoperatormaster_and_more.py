# Generated by Django 4.2.20 on 2025-06-22 18:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("lips", "0017_devicemaster_assigned_at"),
    ]

    operations = [
        migrations.DeleteModel(
            name="BusinessOperatorMaster",
        ),
        migrations.RemoveField(
            model_name="devicemaster",
            name="assigned_at",
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="approval_message",
            field=models.TextField(
                blank=True, help_text="Approval/rejection message", null=True
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="approved_at",
            field=models.DateTimeField(
                blank=True,
                help_text="Timestamp when work was approved/rejected",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="work_approval_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("approved", "Approved"),
                    ("rejected", "Rejected"),
                ],
                default="pending",
                help_text="Work approval status",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="usermaster",
            name="user_id",
            field=models.IntegerField(
                blank=True,
                help_text="Reference to User ID from usermanagement app",
                null=True,
            ),
        ),
    ]
