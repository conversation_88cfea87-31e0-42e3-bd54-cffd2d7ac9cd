# Generated manually to fix the missing table issue
from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('lips', '0005_auto_20250511_1809'),
    ]

    operations = [
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS lips_device_receive_data (
                id VARCHAR(100) NOT NULL PRIMARY KEY,
                device_id INT,
                time VARCHAR(100),
                previous_alert_id VARCHAR(100),
                battery BIGINT,
                dop BIGINT,
                ns_latitude_identifier VARCHAR(100),
                latitude BIGINT,
                ew_longitude_identifier VARCHAR(100),
                longitude BIGINT,
                x_acceleration BIGINT,
                y_acceleration BIGINT,
                z_acceleration BIGINT,
                alive_count BIGINT,
                created_at VARCHAR(100),
                updated_at VARCHAR(100),
                FOREIGN KEY (device_id) REFERENCES lips_device_masters(id) ON DELETE SET NULL
            );
            """,
            reverse_sql="""
            DROP TABLE IF EXISTS lips_device_receive_data;
            """
        ),
    ]
