from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from usermanagement.permissions import (IsAdminOrManager,
                                        IsAdminOrManagerOrReadOnly)

from .models import (DeviceMaster, PermitedApproachInfo, SettingsInfo,
                     UserMaster, WorkInfo)
from .serializers import (DeviceMasterSerializer, DeviceSettingsSerializer,
                          DeviceStatusSerializer,
                          UpdateDeviceSettingsSerializer, WorkDetailSerializer,
                          WorkGroupSerializer, WorkInfoSerializer)


class DeviceViewSet(viewsets.ModelViewSet):
    """ViewSet for DeviceMaster model"""
    queryset = DeviceMaster.objects.all()
    serializer_class = DeviceMasterSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'put']

    def list(self, request):
        """Get all devices"""
        devices = self.get_queryset()
        serializer = self.get_serializer(devices, many=True)
        return Response({
            'success': True,
            'data': {
                'devices': serializer.data
            }
        })

    def retrieve(self, request, pk=None):
        """Get device by ID"""
        try:
            device = self.get_object()
            serializer = self.get_serializer(device)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except Exception as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['put'], url_path='assign')
    def assign(self, request, pk=None):
        """Assign or remove a work assignment for a device"""
        device = self.get_object()
        serializer = DeviceAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            work_name = serializer.validated_data['assignedWork']
            work = WorkInfo.objects.filter(work_name=work_name).first()

            if work:
                device.work_id = work
                device.save()

                # Return updated device
                device_serializer = self.get_serializer(device)
                return Response({
                    'success': True,
                    'message': 'デバイスの割り当てが更新されました',
                    'data': device_serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'message': f"Work '{work_name}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class DeviceSettingsViewSet(viewsets.ViewSet):
    """
    ViewSet for device settings

    This ViewSet provides endpoints to manage device settings including:
    - Listing all device settings
    - Retrieving settings for a specific device
    - Creating new device settings
    - Updating existing device settings
    """
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        """
        Override get_permissions to use different permission classes for different actions
        """
        if self.action in ['update', 'create']:
            permission_classes = [IsAdminOrManager]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    @swagger_auto_schema(
        operation_summary="Get all device settings",
        operation_description="Retrieves settings for all devices. Requires admin or manager role.",
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'deviceSettings': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            'id': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID'),
                                            'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                            'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                            'workId': openapi.Schema(type=openapi.TYPE_STRING, description='Work ID', nullable=True),
                                            'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                            'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                            'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                            'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                            'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                            'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                            'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                            'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                            'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                                        }
                                    )
                                )
                            }
                        )
                    }
                )
            )
        }
    )
    def list(self, request):
        """Get all device settings"""
        devices = DeviceMaster.objects.all()
        serializer = DeviceSettingsSerializer(devices, many=True)
        return Response({
            'success': True,
            'data': {
                'deviceSettings': serializer.data
            }
        })

    @swagger_auto_schema(
        operation_summary="Get device settings by ID",
        operation_description="Retrieves settings for a specific device. Requires admin or manager role.",
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_STRING, description='Work ID', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def retrieve(self, request, pk=None):
        """Get settings for a specific device"""
        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = DeviceSettingsSerializer(device)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_summary="Create device settings",
        operation_description="Creates settings for a device. Requires admin or manager role. The ID field is auto-generated by Django and should NOT be included in the request.",
        request_body=UpdateDeviceSettingsSerializer,
        responses={
            201: openapi.Response(
                description="Settings created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Auto-generated Device ID (integer)'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def create(self, request):
        """Create settings for a device"""
        # Explicitly check if user is admin or manager
        if not request.user.role or request.user.role.name not in ['admin', 'manager']:
            return Response({
                'success': False,
                'message': 'Only admin or manager users are allowed to modify data.'
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = UpdateDeviceSettingsSerializer(data=request.data)

        if serializer.is_valid():
            # Check if device exists
            device_id = request.data.get('deviceId')
            if not device_id:
                return Response({
                    'success': False,
                    'message': 'Device ID is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                device = DeviceMaster.objects.get(display_device_id=device_id)

                # Update device name if provided
                if 'name' in serializer.validated_data:
                    device.device_name = serializer.validated_data['name']

                # Update signal period if provided
                if 'signalPeriod' in serializer.validated_data:
                    device.signal_period = serializer.validated_data['signalPeriod']

                # Update approach area distance if provided
                if 'approachAreaDistance' in serializer.validated_data:
                    device.approach_area_distance = serializer.validated_data['approachAreaDistance']

                # Update approach area seconds if provided
                if 'approachAreaSeconds' in serializer.validated_data:
                    device.approach_area_seconds = serializer.validated_data['approachAreaSeconds']

                device.save()

                # Get user ID from authenticated user
                try:
                    # Import required modules
                    from django.db import IntegrityError
                    from django.utils import timezone

                    # Get the authenticated user's username
                    username = request.user.username
                    # Find the corresponding UserMaster record
                    user = UserMaster.objects.get(id=username)
                    # Set the user_id field on the device
                    device.user_id = user
                    device.save()  # Save the device with the updated user_id

                    # Now update settings with the user ID
                    user_id = user.id

                    # Update approach distance
                    distance_mm = int(serializer.validated_data['approachDistance'] * 1000)  # Convert meters to mm
                    try:
                        # Try to find existing setting with either user_id or user_id_neighborhoodThreshold
                        distance_setting = SettingsInfo.objects.filter(
                            key_name='neighborhoodThreshold'
                        ).filter(
                            id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                        ).first()

                        if distance_setting:
                            # Update existing setting
                            distance_setting.value = str(distance_mm)
                            distance_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            distance_setting.save()
                        else:
                            # Try to create with user_id as id
                            try:
                                distance_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                distance_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_neighborhoodThreshold",
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                    except Exception as e:
                        print(f"Error updating neighborhoodThreshold: {e}")

                    # Update approach seconds
                    seconds = serializer.validated_data['approachSeconds']
                    try:
                        # Try to find existing setting with either user_id or user_id_estimationSec
                        seconds_setting = SettingsInfo.objects.filter(
                            key_name='estimationSec'
                        ).filter(
                            id__in=[user_id, f"{user_id}_estimationSec"]
                        ).first()

                        if seconds_setting:
                            # Update existing setting
                            seconds_setting.value = str(seconds)
                            seconds_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            seconds_setting.save()
                        else:
                            # Try to create with user_id as id
                            try:
                                seconds_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                seconds_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_estimationSec",
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                    except Exception as e:
                        print(f"Error updating estimationSec: {e}")
                except UserMaster.DoesNotExist:
                    # If no UserMaster record exists for this user, log a warning but continue
                    print(f"Warning: No UserMaster record found for authenticated user '{request.user.username}'")
                    # We don't update settings in this case

                # Return created settings
                response_serializer = DeviceSettingsSerializer(device)
                return Response({
                    'success': True,
                    'message': 'デバイス設定が作成されました',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)

            except DeviceMaster.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Device not found'
                }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_summary="Update device settings",
        operation_description="Updates settings for a specific device. Requires admin or manager role.",
        request_body=UpdateDeviceSettingsSerializer,
        responses={
            200: openapi.Response(
                description="Settings updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID (integer)'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Display device ID'),
                                'workId': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)', nullable=True),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name', nullable=True),
                                'workTime': openapi.Schema(type=openapi.TYPE_STRING, description='Work time', nullable=True),
                                'battery': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery level', nullable=True),
                                'signalPeriod': openapi.Schema(type=openapi.TYPE_INTEGER, description='Signal period in seconds', nullable=True),
                                'approachDistance': openapi.Schema(type=openapi.TYPE_NUMBER, description='Approach distance in meters'),
                                'approachSeconds': openapi.Schema(type=openapi.TYPE_INTEGER, description='Approach time in seconds'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'createdAt': openapi.Schema(type=openapi.TYPE_STRING, description='Creation timestamp', nullable=True),
                                'updatedAt': openapi.Schema(type=openapi.TYPE_STRING, description='Last update timestamp', nullable=True),
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def update(self, request, pk=None):
        """Update settings for a specific device"""
        # Explicitly check if user is admin or manager
        print(f"User: {request.user.username}, Role: {request.user.role.name if request.user.role else 'None'}")
        if not request.user.role or request.user.role.name not in ['admin', 'manager']:
            print(f"Permission denied for user {request.user.username} with role {request.user.role.name if request.user.role else 'None'}")
            return Response({
                'success': False,
                'message': 'Only admin or manager users are allowed to modify data.'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = UpdateDeviceSettingsSerializer(data=request.data)

            if serializer.is_valid():
                # Update device fields if provided
                if 'name' in serializer.validated_data:
                    device.device_name = serializer.validated_data['name']

                if 'deviceId' in serializer.validated_data:
                    device.display_device_id = serializer.validated_data['deviceId']

                if 'signalPeriod' in serializer.validated_data:
                    device.signal_period = serializer.validated_data['signalPeriod']

                if 'approachAreaDistance' in serializer.validated_data:
                    device.approach_area_distance = serializer.validated_data['approachAreaDistance']

                if 'approachAreaSeconds' in serializer.validated_data:
                    device.approach_area_seconds = serializer.validated_data['approachAreaSeconds']

                if 'status' in serializer.validated_data:
                    device.status = serializer.validated_data['status']

                # Get user ID from authenticated user
                try:
                    # Get the authenticated user's username
                    username = request.user.username
                    # Find the corresponding UserMaster record
                    user = UserMaster.objects.get(id=username)
                    # Set the user_id field on the device
                    device.user_id = user
                except UserMaster.DoesNotExist:
                    # If no UserMaster record exists for this user, log a warning but continue
                    print(f"Warning: No UserMaster record found for authenticated user '{username}'")
                    # We don't return an error here as we want to allow the update to proceed

                if 'workId' in serializer.validated_data:
                    try:
                        work = WorkInfo.objects.get(id=serializer.validated_data['workId'])
                        device.work_id = work
                    except WorkInfo.DoesNotExist:
                        return Response({
                            'success': False,
                            'message': f"Work with ID '{serializer.validated_data['workId']}' not found"
                        }, status=status.HTTP_400_BAD_REQUEST)

                if 'workTime' in serializer.validated_data:
                    device.work_time = serializer.validated_data['workTime']

                if 'battery' in serializer.validated_data:
                    device.battery = serializer.validated_data['battery']

                if 'previousAlertInstruction' in serializer.validated_data:
                    device.previous_alert_instruction = serializer.validated_data['previousAlertInstruction']
                if 'assignedAt' in serializer.validated_data:
                    device.approved_at = serializer.validated_data['assignedAt']

                # Update the updated_at field
                from django.utils import timezone
                device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')

                device.save()

                # Update settings
                user_id = device.user_id.id if device.user_id else None
                if user_id:
                    from django.db import IntegrityError
                    from django.utils import timezone

                    # Update approach distance
                    distance_mm = int(serializer.validated_data['approachDistance'] * 1000)  # Convert meters to mm
                    try:
                        # Try to find existing setting with either user_id or user_id_neighborhoodThreshold
                        distance_setting = SettingsInfo.objects.filter(
                            key_name='neighborhoodThreshold'
                        ).filter(
                            id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                        ).first()

                        if distance_setting:
                            # Update existing setting
                            distance_setting.value = str(distance_mm)
                            distance_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            distance_setting.save()
                            print(f"Updated neighborhoodThreshold setting: {distance_setting.id} = {distance_setting.value}")
                        else:
                            # Try to create with user_id as id
                            try:
                                distance_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created neighborhoodThreshold setting: {distance_setting.id} = {distance_setting.value}")
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                distance_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_neighborhoodThreshold",
                                    key_name='neighborhoodThreshold',
                                    value=str(distance_mm),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created neighborhoodThreshold setting with unique ID: {distance_setting.id} = {distance_setting.value}")
                    except Exception as e:
                        print(f"Error updating neighborhoodThreshold: {e}")

                    # Update approach seconds
                    seconds = serializer.validated_data['approachSeconds']
                    try:
                        # Try to find existing setting with either user_id or user_id_estimationSec
                        seconds_setting = SettingsInfo.objects.filter(
                            key_name='estimationSec'
                        ).filter(
                            id__in=[user_id, f"{user_id}_estimationSec"]
                        ).first()

                        if seconds_setting:
                            # Update existing setting
                            seconds_setting.value = str(seconds)
                            seconds_setting.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                            seconds_setting.save()
                            print(f"Updated estimationSec setting: {seconds_setting.id} = {seconds_setting.value}")
                        else:
                            # Try to create with user_id as id
                            try:
                                seconds_setting = SettingsInfo.objects.create(
                                    id=user_id,
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created estimationSec setting: {seconds_setting.id} = {seconds_setting.value}")
                            except IntegrityError:
                                # If that fails, create with a unique ID
                                seconds_setting = SettingsInfo.objects.create(
                                    id=f"{user_id}_estimationSec",
                                    key_name='estimationSec',
                                    value=str(seconds),
                                    created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                                    updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                                )
                                print(f"Created estimationSec setting with unique ID: {seconds_setting.id} = {seconds_setting.value}")
                    except Exception as e:
                        print(f"Error updating estimationSec: {e}")

                # Refresh the device from the database to get the latest settings
                device.refresh_from_db()

                # Create a custom response with the updated settings
                response_data = {
                    'id': device.id,
                    'name': device.device_name,
                    'deviceId': device.display_device_id,
                    'userId': device.user_id.id if device.user_id else None,
                    'workId': device.work_id.id if device.work_id else None,
                    'workName': device.work_id.work_name if device.work_id else None,
                    'workTime': device.work_time,
                    'battery': device.battery,
                    'previousAlertInstruction': device.previous_alert_instruction,
                    'signalPeriod': device.signal_period,
                    'approachAreaDistance': device.approach_area_distance,
                    'approachAreaSeconds': device.approach_area_seconds,
                    'status': device.status or 'active',
                    'createdAt': str(device.created_at) if device.created_at else None,
                    'updatedAt': str(device.updated_at) if device.updated_at else None,
                }

                # Add the updated settings values
                if user_id:
                    # Get approach distance from settings
                    distance_setting = SettingsInfo.objects.filter(
                        key_name='neighborhoodThreshold'
                    ).filter(
                        id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
                    ).first()

                    if distance_setting:
                        response_data['approachDistance'] = float(distance_setting.value) / 1000  # Convert mm to meters
                    else:
                        response_data['approachDistance'] = 5.0  # Default value

                    # Get approach seconds from settings
                    seconds_setting = SettingsInfo.objects.filter(
                        key_name='estimationSec'
                    ).filter(
                        id__in=[user_id, f"{user_id}_estimationSec"]
                    ).first()

                    if seconds_setting:
                        response_data['approachSeconds'] = int(seconds_setting.value)
                    else:
                        response_data['approachSeconds'] = device.signal_period or 30
                else:
                    response_data['approachDistance'] = 5.0  # Default value
                    response_data['approachSeconds'] = device.signal_period or 30

                return Response({
                    'success': True,
                    'message': 'デバイス設定が更新されました',
                    'data': response_data
                })

            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)


class WorkPagination(PageNumberPagination):
    """Pagination class for works"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


class WorksView(APIView):
    """
    View for works/operations

    This view provides endpoints to:
    - List all works (with optional pagination and detailed information)
    - Create a new work
    """
    permission_classes = [IsAuthenticated]
    pagination_class = WorkPagination

    @swagger_auto_schema(
        operation_summary="Get all works",
        operation_description="Retrieves a list of all works. Supports two modes: simple list of work names or paginated detailed list. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='detailed',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_BOOLEAN,
                description='If true, returns detailed information about all works with pagination',
                required=False
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Page number (only used when detailed=true)',
                required=False
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Number of items per page (max 100, only used when detailed=true)',
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'works': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_STRING),
                                    description='List of work names (when detailed=false)'
                                )
                            }
                        )
                    }
                )
            )
        }
    )
    def get(self, request):
        """Get all works/operations"""
        # Check if detailed view is requested
        detailed = request.query_params.get('detailed', 'false').lower() == 'true'

        if detailed:
            # Return paginated detailed list
            paginator = self.pagination_class()
            works = WorkInfo.objects.all().order_by('id')
            result_page = paginator.paginate_queryset(works, request)
            serializer = WorkInfoSerializer(result_page, many=True)  # Use WorkInfoSerializer to get all fields

            return paginator.get_paginated_response({
                'success': True,
                'data': serializer.data
            })
        else:
            # Return simple list of work names (original behavior)
            works = WorkInfo.objects.all()  # Fetch all works
            serializer = WorkInfoSerializer(works, many=True)  # Serialize all fields
            return Response({
                'success': True,
                'data': {
                    'works': serializer.data
                }
            })

    @swagger_auto_schema(
        operation_summary="Create a new work",
        operation_description="Creates a new work. Requires admin or manager role. The ID field is auto-generated by Django and should NOT be included in the request.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['work_name', 'group_num'],
            properties={
                'work_name': openapi.Schema(type=openapi.TYPE_STRING, description='Name of the work (required)'),
                'group_num': openapi.Schema(type=openapi.TYPE_STRING, description='Group number (required)')
            }
        ),
        responses={
            201: openapi.Response(
                description="Work created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Auto-generated Work ID (integer)'),
                                'work_name': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                                'group_num': openapi.Schema(type=openapi.TYPE_STRING, description='Group number')
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Create a new work"""
        serializer = WorkInfoSerializer(data=request.data)

        if serializer.is_valid():
            work = serializer.save()
            return Response({
                'success': True,
                'message': 'Work created successfully',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)





class WorkDetailView(APIView):
    """
    View for work details

    This view provides endpoints to:
    - Get detailed information for a specific work
    - Delete a specific work
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Get work details by ID",
        operation_description="Retrieves detailed information for a specific work by ID. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='work_id',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_INTEGER,
                description='Work ID (integer)',
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Success",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Work ID (integer)'),
                                'workName': openapi.Schema(type=openapi.TYPE_STRING, description='Work name'),
                                'groupNumber': openapi.Schema(type=openapi.TYPE_STRING, description='Group number'),
                                'assignedDevices': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of assigned devices'),
                                'timeElapsed': openapi.Schema(type=openapi.TYPE_STRING, description='Time elapsed'),
                                'accessibleAreas': openapi.Schema(type=openapi.TYPE_INTEGER, description='Number of accessible areas')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def get(self, request, work_id):
        """Get detailed information for a specific work"""
        try:
            work = WorkInfo.objects.get(id=work_id)
            serializer = WorkGroupSerializer(work)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f"Work with ID '{work_id}' not found"
            }, status=status.HTTP_404_NOT_FOUND)

    @swagger_auto_schema(
        operation_summary="Delete a work by ID",
        operation_description="Deletes a specific work by ID. Requires admin or manager role.",
        manual_parameters=[
            openapi.Parameter(
                name='work_id',
                in_=openapi.IN_PATH,
                type=openapi.TYPE_INTEGER,
                description='Work ID (integer)',
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Work deleted successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message')
                    }
                )
            ),
            404: openapi.Response(
                description="Work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Cannot delete work with assigned devices",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def delete(self, request, work_id):
        """Delete a specific work"""
        try:
            work = WorkInfo.objects.get(id=work_id)

            # Check if any devices are assigned to this work
            assigned_devices = DeviceMaster.objects.filter(work_id=work.id).count()
            if assigned_devices > 0:
                return Response({
                    'success': False,
                    'message': f"Cannot delete work with ID '{work_id}' because it has {assigned_devices} assigned devices"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Store work name for the response message
            work_name = work.work_name

            # Delete the work
            work.delete()

            return Response({
                'success': True,
                'message': f"作業 '{work_name}' が削除されました"  # Work has been deleted
            })

        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': f"Work with ID '{work_id}' not found"
            }, status=status.HTTP_404_NOT_FOUND)


class DeviceAssignWorkView(APIView):
    """
    View for assigning work to a device

    This view provides an endpoint to:
    - Assign work to a device and increment assignedDevices count for the work
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Assign work to a device",
        operation_description="Assigns work to a device and increments assignedDevices count for the work. Requires admin or manager role.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['deviceId', 'assignedWork'],
            properties={
                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Work name')
            }
        ),
        responses={
            200: openapi.Response(
                description="Work assigned successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Assigned work name'),
                                'charge': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery charge'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'usageTime': openapi.Schema(type=openapi.TYPE_STRING, description='Usage time'),
                                'assignedAt': openapi.Schema(type=openapi.FORMAT_DATETIME, description='Time since (re-)assigned to work')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device or work not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Assign work to a device"""
        device_id = request.data.get('deviceId')
        if not device_id:
            return Response({
                'success': False,
                'message': 'Device ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign deviceId to display_device_id
        request.data['display_device_id'] = device_id
        del request.data['deviceId']

        work_id = request.data.get('assignedWork')
        if not work_id:
            return Response({
                'success': False,
                'message': 'Work ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign workId to work_id
        request.data['work_id'] = work_id
        del request.data['assignedWork']

        serializer = DeviceMasterSerializer(data=request.data)

        if serializer.is_valid():
            device_id = serializer.validated_data['display_device_id']
            work = serializer.validated_data['work_id']


            # Find the work
            work = WorkInfo.objects.filter(id=work.id).first()

            if not work:
                return Response({
                    'success': False,
                    'message': f"Work '{work_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)


            if DeviceMaster.objects.filter(work_id=work.id, display_device_id=device_id).exists():
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' is already assigned to work '{work.work_name}'"
                }, status=status.HTTP_400_BAD_REQUEST)

            device, created = DeviceMaster.objects.get_or_create(
                display_device_id=device_id,
                defaults={
                    'device_name': request.data.get('name', 'Unnamed Device'),
                    'signal_period': request.data.get('signalPeriod', 60),
                    'work_time': request.data.get('workTime', None),
                    'battery': request.data.get('battery', None),
                    'previous_alert_instruction': request.data.get('previousAlertInstruction', None)
                }
            )

            # Update fields if the device already exists and data is provided
            if not created:
                if 'name' in request.data:
                    device.device_name = request.data['name']
                if 'signalPeriod' in request.data:
                    device.signal_period = request.data['signalPeriod']
                if 'workTime' in request.data:
                    device.work_time = request.data['workTime']
                if 'battery' in request.data:
                    device.battery = request.data['battery']
                if 'previousAlertInstruction' in request.data:
                    device.previous_alert_instruction = request.data['previousAlertInstruction']
                device.save()

            # Assign work to the device
            device.work_id = work
            device.assigned_at = timezone.now()  # Add timestamp to change
            device.save()

            # Serialize the updated device
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device work assigned successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
class DeviceRemoveWorkView(APIView):
    """
    View for removing work from a device

    This view provides an endpoint to:
    - Remove work from a device and decrement assignedDevices count for the work
    """
    permission_classes = [IsAdminOrManager]

    @swagger_auto_schema(
        operation_summary="Remove work from a device",
        operation_description="Removes work from a device and decrements assignedDevices count for the work. Requires admin or manager role.",
        request_body=DeviceMasterSerializer,
        responses={
            200: openapi.Response(
                description="Work removed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'name': openapi.Schema(type=openapi.TYPE_STRING, description='Device name'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'assignedWork': openapi.Schema(type=openapi.TYPE_STRING, description='Assigned work name', nullable=True),
                                'charge': openapi.Schema(type=openapi.TYPE_INTEGER, description='Battery charge'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status'),
                                'usageTime': openapi.Schema(type=openapi.TYPE_STRING, description='Usage time'),
                                'assignedAt': openapi.Schema(type=openapi.FORMAT_DATETIME, description='Time since (re-)assigned to work', nullable=True)
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Remove work from a device"""
        #from the request display_device_id is coming as deviceId
        device_id = request.data.get('deviceId')
        if not device_id:
            return Response({
                'success': False,
                'message': 'Device ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign deviceId to display_device_id
        request.data['display_device_id'] = device_id
        del request.data['deviceId']

        #work_id is coming as workId
        work_id = request.data.get('workId')
        if not work_id:
            return Response({
                'success': False,
                'message': 'Work ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        #assign workId to work_id
        request.data['work_id'] = work_id
        del request.data['workId']

        serializer = DeviceMasterSerializer(data=request.data)

        if serializer.is_valid():
            # device_id = serializer.validated_data['deviceId']

            # Find the device
            device = DeviceMaster.objects.filter(display_device_id=device_id).first()
            if not device:
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

            # Remove work assignment
            device.work_id = None
            device.assigned_at = None  # Remove timestamp if there is no assigned work
            device.save()

            # Return updated device
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device work removed successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class DeviceStatusView(APIView):
    """
    View for getting device status

    This view provides an endpoint to:
    - Get the current status of a device by its device ID
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Get device status",
        operation_description="Gets the current status of a device by its device ID. Requires admin or manager role.",
        request_body=DeviceStatusSerializer,
        responses={
            200: openapi.Response(
                description="Device status retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Success message'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Device ID'),
                                'deviceId': openapi.Schema(type=openapi.TYPE_STRING, description='Device ID (display_device_id)'),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, description='Device status')
                            }
                        )
                    }
                )
            ),
            404: openapi.Response(
                description="Device not found",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            ),
            400: openapi.Response(
                description="Invalid input",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Success status'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, description='Error message')
                    }
                )
            )
        }
    )
    def post(self, request):
        """Get device status"""
        serializer = DeviceStatusSerializer(data=request.data)

        if serializer.is_valid():
            device_id = serializer.validated_data['deviceId']
            work_id= serializer.validated_data.get('workId')

            # Find the device
            device = DeviceMaster.objects.filter(display_device_id=device_id, work_id__id=work_id).first()
            if not device:
                return Response({
                    'success': False,
                    'message': f"Device with ID '{device_id}' and work ID '{work_id}' not found"
                }, status=status.HTTP_404_NOT_FOUND)

            # Return all device data
            device_serializer = DeviceMasterSerializer(device)
            return Response({
                'success': True,
                'message': 'Device data retrieved successfully',
                'data': device_serializer.data
            })

        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


from rest_framework import status, viewsets
from rest_framework.response import Response

from .models import ActiveWork
from .serializers import ActiveWorkSerializer

# from rest_framework.permissions import IsAuthenticated # Uncomment if authentication is needed

class ActiveWorkViewSet(viewsets.ModelViewSet):
    """ViewSet for managing ActiveWork records"""
    queryset = ActiveWork.objects.all()
    serializer_class = ActiveWorkSerializer
    # permission_classes = [IsAuthenticated] # Add authentication if needed

    def create(self, request, *args, **kwargs):
        data = request.data
        
        # Check if work_ids is provided and process accordingly
        if 'work_ids' in data and isinstance(data['work_ids'], list) and data['work_ids']:
            active_works = []
            
            # Process each work_id and create/update ActiveWork objects
            for work_id in data['work_ids']:
                try:
                    # Try to find an existing ActiveWork that corresponds to this work_id
                    # Note: This is an implementation choice - we're assuming there's a 1:1 relationship
                    # between WorkInfo and ActiveWork. You might need to adjust this logic.
                    work_info = WorkInfo.objects.get(id=work_id)
                    
                    # Prepare data for this work
                    work_data = {
                        'id': work_info.id,
                        'work_name': work_info.work_name,
                        'group_number': work_info.group_num,
                        'assigned_devices': DeviceMaster.objects.filter(work_id=work_info.id).count(),
                        'time_elapsed': "07:48:59.037",  # Same as in WorkGroupSerializer
                        'accessible_areas': PermitedApproachInfo.objects.filter(work_id=work_info.id).count(),
                    }
                    
                    # Create or update the ActiveWork
                    active_work, created = ActiveWork.objects.update_or_create(
                        # This assumes work_name and group_number together uniquely identify a work
                        # You might need a different unique identifier
                        work_name=work_info.work_name,
                        group_number=work_info.group_num,
                        defaults=work_data
                    )
                    
                    active_works.append(active_work)
                except WorkInfo.DoesNotExist:
                    # Skip non-existent work IDs with a warning
                    print(f"Warning: WorkInfo with ID {work_id} does not exist")
            
            # Serialize all active works
            serializer = self.get_serializer(active_works, many=True)
            headers = self.get_success_headers(serializer.data)
            return Response({"success": True, "data": serializer.data}, status=status.HTTP_201_CREATED, headers=headers)
            
        # If no work_ids or empty list, fall back to standard processing
        return super().create(request, *args, **kwargs)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response({"success": True, "data": serializer.data})

        serializer = self.get_serializer(queryset, many=True)
        return Response({"success": True, "data": serializer.data})

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({"success": True, "data": serializer.data})

    def update(self, request, *args, **kwargs):
        data = request.data
        
        # Handle work_ids updates similarly to create method
        if 'work_ids' in data and isinstance(data['work_ids'], list) and data['work_ids']:
            instance = self.get_object()
            updated = False
            
            # Update this instance based on the first work_id
            work_id = data['work_ids'][0]
            try:
                work_info = WorkInfo.objects.get(id=work_id)
                
                # Update fields from WorkInfo
                instance.work_name = work_info.work_name
                instance.group_number = work_info.group_num
                instance.assigned_devices = DeviceMaster.objects.filter(work_id=work_info.id).count()
                instance.time_elapsed = "07:48:59.037"  # Same as in WorkGroupSerializer
                instance.accessible_areas = PermitedApproachInfo.objects.filter(work_id=work_info.id).count()
                instance.save()
                updated = True
                
                # Process additional work_ids if needed (create or update other ActiveWork records)
                for additional_work_id in data['work_ids'][1:]:
                    try:
                        work_info = WorkInfo.objects.get(id=additional_work_id)
                        
                        # Prepare data for this work
                        work_data = {
                            'id': work_info.id,
                            'work_name': work_info.work_name,
                            'group_number': work_info.group_num,
                            'assigned_devices': DeviceMaster.objects.filter(work_id=work_info.id).count(),
                            'time_elapsed': "07:48:59.037",  # Same as in WorkGroupSerializer
                            'accessible_areas': PermitedApproachInfo.objects.filter(work_id=work_info.id).count(),
                        }
                        
                        # Create or update the ActiveWork
                        ActiveWork.objects.update_or_create(
                            # This assumes work_name and group_number together uniquely identify a work
                            work_name=work_info.work_name,
                            group_number=work_info.group_num,
                            defaults=work_data
                        )
                    except WorkInfo.DoesNotExist:
                        # Skip non-existent work IDs with a warning
                        print(f"Warning: WorkInfo with ID {additional_work_id} does not exist")
            
            except WorkInfo.DoesNotExist:
                raise serializers.ValidationError(f"WorkInfo with ID {work_id} does not exist")
            
            if updated:
                serializer = self.get_serializer(instance)
                return Response({"success": True, "data": serializer.data})
        
        # If no work_ids, fall back to standard processing
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        # Standard practice is to return 204 No Content on successful deletion
        return Response({"success": True, "message": "ActiveWork deleted successfully."}, status=status.HTTP_204_NO_CONTENT)
