# Generated by Django 4.2.20 on 2025-06-22 18:59

from django.db import migrations, models


def safe_delete_table(apps, schema_editor):
    """Safely delete the BusinessOperatorMaster table if it exists"""
    with schema_editor.connection.cursor() as cursor:
        # Check if the table exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_business_operator_masters'
        """)
        table_exists = cursor.fetchone()[0] > 0
        
        if table_exists:
            cursor.execute("DROP TABLE lips_business_operator_masters")


def reverse_safe_delete_table(apps, schema_editor):
    """Reverse operation - recreate the table if needed"""
    # We don't need to recreate the table as it's being removed intentionally
    pass


def safe_remove_field(apps, schema_editor):
    """Safely remove the assigned_at field if it exists"""
    with schema_editor.connection.cursor() as cursor:
        # Check if the column exists
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_device_masters'
            AND column_name = 'assigned_at'
        """)
        column_exists = cursor.fetchone()[0] > 0
        
        if column_exists:
            cursor.execute("ALTER TABLE lips_device_masters DROP COLUMN assigned_at")


def reverse_safe_remove_field(apps, schema_editor):
    """Reverse operation - add back the assigned_at field if needed"""
    # We don't need to recreate the column as it's being removed intentionally
    pass


def safe_add_fields(apps, schema_editor):
    """Safely add new fields if they don't exist"""
    with schema_editor.connection.cursor() as cursor:
        # Check and add approval_message field
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_device_masters'
            AND column_name = 'approval_message'
        """)
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE lips_device_masters 
                ADD COLUMN approval_message TEXT NULL 
                COMMENT 'Approval/rejection message'
            """)
        
        # Check and add approved_at field
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_device_masters'
            AND column_name = 'approved_at'
        """)
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE lips_device_masters 
                ADD COLUMN approved_at DATETIME NULL 
                COMMENT 'Timestamp when work was approved/rejected'
            """)
        
        # Check and add work_approval_status field
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_device_masters'
            AND column_name = 'work_approval_status'
        """)
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE lips_device_masters 
                ADD COLUMN work_approval_status VARCHAR(20) NOT NULL DEFAULT 'pending' 
                COMMENT 'Work approval status'
            """)
        
        # Check and add user_id field to lips_user_masters
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'lips_user_masters'
            AND column_name = 'user_id'
        """)
        if cursor.fetchone()[0] == 0:
            cursor.execute("""
                ALTER TABLE lips_user_masters 
                ADD COLUMN user_id INT NULL 
                COMMENT 'Reference to User ID from usermanagement app'
            """)


def reverse_safe_add_fields(apps, schema_editor):
    """Reverse operation - remove the added fields"""
    with schema_editor.connection.cursor() as cursor:
        # Remove fields if they exist
        fields_to_remove = [
            ('lips_device_masters', 'approval_message'),
            ('lips_device_masters', 'approved_at'),
            ('lips_device_masters', 'work_approval_status'),
            ('lips_user_masters', 'user_id')
        ]
        
        for table_name, column_name in fields_to_remove:
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = '{table_name}'
                AND column_name = '{column_name}'
            """)
            if cursor.fetchone()[0] > 0:
                cursor.execute(f"ALTER TABLE {table_name} DROP COLUMN {column_name}")


class Migration(migrations.Migration):

    dependencies = [
        ("lips", "0017_devicemaster_assigned_at"),
    ]

    operations = [
        # Use RunPython instead of DeleteModel to safely handle the table deletion
        migrations.RunPython(
            safe_delete_table,
            reverse_safe_delete_table,
        ),
        # Use RunPython instead of RemoveField to safely handle the column removal
        migrations.RunPython(
            safe_remove_field,
            reverse_safe_remove_field,
        ),
        # Use RunPython to safely add all fields
        migrations.RunPython(
            safe_add_fields,
            reverse_safe_add_fields,
        ),
    ]
