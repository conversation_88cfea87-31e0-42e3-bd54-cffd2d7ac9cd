# Generated by Django 4.2.20 on 2025-05-20 15:06

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0012_fix_database_structure"),
    ]

    operations = [
        migrations.AddField(
            model_name="devicemaster",
            name="approach_area_distance",
            field=models.FloatField(
                blank=True, help_text="Approach area distance in meters", null=True
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="approach_area_seconds",
            field=models.IntegerField(
                blank=True, help_text="Approach area time in seconds", null=True
            ),
        ),
    ]
