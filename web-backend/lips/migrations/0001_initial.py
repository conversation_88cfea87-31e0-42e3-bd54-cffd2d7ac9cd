# Generated by Django 4.2.20 on 2025-05-11 13:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AlertInstructionManagementInfo",
            fields=[
                ("id", models.IntegerField(primary_key=True, serialize=False)),
            ],
            options={
                "db_table": "lips_alert_instruction_management_info",
            },
        ),
        migrations.CreateModel(
            name="DeviceMaster",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                (
                    "display_device_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "device_name",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("work_time", models.CharField(blank=True, max_length=100, null=True)),
                ("battery", models.BigInteger<PERSON>ield(blank=True, null=True)),
                (
                    "previous_alert_instruction",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("signal_period", models.BigIntegerField(blank=True, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "db_table": "lips_device_masters",
            },
        ),
        migrations.CreateModel(
            name="FixInfo",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("key_name", models.CharField(blank=True, max_length=100, null=True)),
                ("val", models.CharField(blank=True, max_length=100, null=True)),
                ("summary", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "db_table": "lips_fix_info",
            },
        ),
        migrations.CreateModel(
            name="ProhibitedApproachInfo",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("latitude", models.FloatField(blank=True, null=True)),
                ("longitude", models.FloatField(blank=True, null=True)),
                ("x_vector", models.CharField(blank=True, max_length=100, null=True)),
                ("y_vector", models.CharField(blank=True, max_length=100, null=True)),
                ("base_area", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "extraction_area",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("map_code", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "prefectures",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "municipalities",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "db_table": "lips_prohibited_approach_info",
            },
        ),
        migrations.CreateModel(
            name="SettingsInfo",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("key_name", models.CharField(blank=True, max_length=100, null=True)),
                ("value", models.CharField(blank=True, max_length=100, null=True)),
                ("summary", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "db_table": "lips_settings_info",
            },
        ),
        migrations.CreateModel(
            name="UserMaster",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "db_table": "lips_user_masters",
            },
        ),
        migrations.CreateModel(
            name="WorkInfo",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("work_name", models.CharField(blank=True, max_length=100, null=True)),
                ("group_num", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "user_id",
                    models.ForeignKey(
                        blank=True,
                        db_column="user_id",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lips.usermaster",
                    ),
                ),
            ],
            options={
                "db_table": "lips_work_info",
            },
        ),
        migrations.CreateModel(
            name="PermitedApproachInfo",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("area_element_num", models.BigIntegerField(blank=True, null=True)),
                ("area_info", models.CharField(blank=True, max_length=1100, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "work_id",
                    models.ForeignKey(
                        blank=True,
                        db_column="work_id",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lips.workinfo",
                    ),
                ),
            ],
            options={
                "db_table": "lips_permited_approach_info",
            },
        ),
        migrations.CreateModel(
            name="HistoricalJudgeAlertInstruction",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                (
                    "ns_latitude_identifier",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("latitude", models.BigIntegerField(blank=True, null=True)),
                (
                    "ew_longitude_identifier",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("longitude", models.BigIntegerField(blank=True, null=True)),
                ("utm_x", models.CharField(blank=True, max_length=100, null=True)),
                ("utm_y", models.CharField(blank=True, max_length=100, null=True)),
                ("x_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("y_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("z_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("alert_instruction", models.BigIntegerField(blank=True, null=True)),
                ("alert_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "previous_alert_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("work_time", models.CharField(blank=True, max_length=100, null=True)),
                ("group_num", models.CharField(blank=True, max_length=100, null=True)),
                ("alive_count", models.BigIntegerField(blank=True, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "device_id",
                    models.ForeignKey(
                        blank=True,
                        db_column="device_id",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lips.devicemaster",
                    ),
                ),
            ],
            options={
                "db_table": "lips_historical_judge_alert_instruction",
            },
        ),
        migrations.CreateModel(
            name="DeviceReceiveData",
            fields=[
                (
                    "id",
                    models.CharField(max_length=100, primary_key=True, serialize=False),
                ),
                ("time", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "previous_alert_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("battery", models.BigIntegerField(blank=True, null=True)),
                ("dop", models.BigIntegerField(blank=True, null=True)),
                (
                    "ns_latitude_identifier",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("latitude", models.BigIntegerField(blank=True, null=True)),
                (
                    "ew_longitude_identifier",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("longitude", models.BigIntegerField(blank=True, null=True)),
                ("x_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("y_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("z_acceleration", models.BigIntegerField(blank=True, null=True)),
                ("alive_count", models.BigIntegerField(blank=True, null=True)),
                ("created_at", models.CharField(blank=True, max_length=100, null=True)),
                ("updated_at", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "device_id",
                    models.ForeignKey(
                        blank=True,
                        db_column="device_id",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="lips.devicemaster",
                    ),
                ),
            ],
            options={
                "db_table": "lips_device_receive_data",
            },
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="user_id",
            field=models.ForeignKey(
                blank=True,
                db_column="user_id",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="lips.usermaster",
            ),
        ),
        migrations.AddField(
            model_name="devicemaster",
            name="work_id",
            field=models.ForeignKey(
                blank=True,
                db_column="work_id",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="lips.workinfo",
            ),
        ),
    ]
