# Generated by Django 4.2.20 on 2025-05-11 18:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0006_create_device_receive_data_table"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="devicemaster",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="workinfo",
            name="id",
            field=models.BigAutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
    ]
