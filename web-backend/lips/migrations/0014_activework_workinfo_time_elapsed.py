# Generated by Django 4.2.20 on 2025-05-22 15:11

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("lips", "0013_devicemaster_approach_area_distance_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ActiveWork",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("work_name", models.Char<PERSON>ield(max_length=255)),
                (
                    "group_number",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                ("assigned_devices", models.IntegerField(default=0)),
                (
                    "time_elapsed",
                    models.Char<PERSON>ield(blank=True, max_length=50, null=True),
                ),
                ("accessible_areas", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTime<PERSON>ield(auto_now=True)),
            ],
            options={
                "verbose_name": "Active Work",
                "verbose_name_plural": "Active Works",
                "db_table": "lips_active_work",
            },
        ),
        migrations.Add<PERSON>ield(
            model_name="workinfo",
            name="time_elapsed",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
    ]
