from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import (ActiveWorksTimeCalculationView, ActiveWorkViewSet,
                    AlertViewSet, DeviceAssignWorkView, DeviceRemoveWorkView,
                    DeviceSettingsViewSet, DeviceStatusView, DeviceViewSet,
                    WorkApprovalView, WorkDetailView, WorkRejectionView,
                    WorksView, WorkTimeCalculationView, WorkTimerUpdateView)

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r'device', DeviceViewSet, basename='device')  # Changed to match Postman collection
router.register(r'works/active-works', ActiveWorkViewSet, basename='active-work-devices')
router.register(r'alerts', AlertViewSet, basename='alerts')

# Register device settings viewset
device_settings_list = DeviceSettingsViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_settings_detail = DeviceSettingsViewSet.as_view({
    'get': 'retrieve',
    'put': 'update'
})

urlpatterns = [
    # Put specific device URLs BEFORE the router to avoid conflicts
    path('device/settings/', device_settings_list, name='device-settings-list'),
    path('device/settings/<str:pk>/', device_settings_detail, name='device-settings-detail'),
    path('device/assign-work/', DeviceAssignWorkView.as_view(), name='device-assign-work'),
    path('device/remove-work/', DeviceRemoveWorkView.as_view(), name='device-remove-work'),
    path('device/status/', DeviceStatusView.as_view(), name='device-status'),
    # Router URLs come after specific patterns
    path('', include(router.urls)),
    path('works/', WorksView.as_view(), name='works-list'),
    # Work Approval APIs - Put specific patterns BEFORE generic ones
    path('works/approve/', WorkApprovalView.as_view(), name='work-approve'),
    path('works/reject/', WorkRejectionView.as_view(), name='work-reject'),
    # New Work Time Management APIs
    path('works/active-works/time-calculation/', ActiveWorksTimeCalculationView.as_view(), name='active-works-time-calculation'),
    path('works/<str:work_id>/time-calculation/', WorkTimeCalculationView.as_view(), name='work-time-calculation'),
    path('works/<str:work_id>/timer/', WorkTimerUpdateView.as_view(), name='work-timer-update'),
    # Generic work detail - Put AFTER specific patterns
    path('works/<str:work_id>/', WorkDetailView.as_view(), name='work-detail'),
]
