from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from .views import (ActiveWorkViewSet, DeviceAssignWorkView,
                    DeviceRemoveWorkView, DeviceSettingsViewSet,
                    DeviceStatusView, DeviceViewSet, WorkDetailView, WorksView)

# Create a router and register our viewsets with it.
router = DefaultRouter()
router.register(r'devices', DeviceViewSet, basename='device')
router.register(r'works/active-works', ActiveWorkViewSet, basename='active-work-devices')

# Register device settings viewset
device_settings_list = DeviceSettingsViewSet.as_view({
    'get': 'list',
    'post': 'create'
})

device_settings_detail = DeviceSettingsViewSet.as_view({
    'get': 'retrieve',
    'put': 'update'
})

urlpatterns = [
    path('', include(router.urls)),
    path('device/settings/', device_settings_list, name='device-settings-list'),
    path('device/settings/<str:pk>/', device_settings_detail, name='device-settings-detail'),
    path('device/assign-work/', DeviceAssignWorkView.as_view(), name='device-assign-work'),
    path('device/remove-work/', DeviceRemoveWorkView.as_view(), name='device-remove-work'),
    path('device/status/', DeviceStatusView.as_view(), name='device-status'),
    path('works/', WorksView.as_view(), name='works-list'),
    path('works/<str:work_id>/', WorkDetailView.as_view(), name='work-detail'),
]
