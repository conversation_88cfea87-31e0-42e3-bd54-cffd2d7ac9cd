# LIPS Web Application

This repository contains the code for the LIPS Web Application built with Django.

## Authentication API

A Django REST Framework application providing robust authentication APIs with JWT token-based authentication.

### Features

- User registration and login
- JWT token authentication
- Password reset functionality
- Change password functionality
- Swagger API documentation

### Tech Stack

- Python 3.10
- Django 4.2
- Django REST Framework
- MySQL
- JWT Authentication
- Swagger/OpenAPI Documentation

### Setup Instructions

# LIPS Web Application

This repository contains the code for the LIPS Web Application built with Django.

## Quick Start

For a simple one-command setup process, please see the [Quick Start Guide](QUICK_START.md).

## Option 1: Using the Setup Script (Recommended)

The easiest way to run this application:

1. Make sure you have Python 3.8+ and MySQL installed
2. Run the setup script:
   - Windows: Double-click `start.bat`
   - macOS/Linux: Run `./start.sh`
3. Follow the on-screen instructions
4. Access the application at http://localhost:8000

## Option 2: Manual Setup

If you prefer to set up manually:

1. Make sure you have Python 3.8+ and MySQL installed
2. Follow the manual setup instructions in the [Quick Start Guide](QUICK_START.md#standard-manual-setup)

## Technical Details

### Database Setup

The setup scripts will automatically create the required database tables and roles. If you're setting up manually, make sure to create the following roles in this exact order:

| id  | name    |
| --- | ------- |
| 1   | admin   |
| 2   | manager |
| 3   | user    |

Database mysql query :
INSERT INTO usermanagement_role (id, name) VALUES
(1, 'admin'),
(2, 'manager'),
(3, 'user');

The application uses MySQL with the following default configuration:
- Database name: lipsweb
- Username: root
- Password: (empty)
- Host: localhost
- Port: 3307

You can customize these settings by editing the `.env` file in the project root.

### API Documentation

Once the application is running, you can access the API documentation at:

- Swagger UI: http://localhost:8000/ (root URL)
- ReDoc: http://localhost:8000/redoc/

### Main API Endpoints

- Authentication & User Management: `/api/v1/userapi/`
- LIPS API: `/api/v1/lipsapi/`
- Notification API: `/api/v1/`

### Default Admin Credentials

When using the setup scripts or Docker, a default admin user is created:

- Username: `admin`
- Password: `admin`

For security reasons, please change this password after your first login.

## Development Information

For developers who want to work on this project:

### Running Tests

```
python manage.py test
```

### Manual Server Start

```
python manage.py runserver
```

### Creating Additional Users

```
python manage.py createsuperuser
```

## Need Help?

For more detailed instructions and troubleshooting, please refer to the [Quick Start Guide](QUICK_START.md).

#### 1. Clone the repository

```bash
git clone <repository-url>
cd web-backend
````

#### 2. Create and activate a virtual environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

#### 3. Install dependencies

```bash
pip install -r requirements.txt
pip install django djangorestframework djangorestframework-simplejwt
```

For MySQL support (production):

```bash
# On macOS, you might need to install MySQL first:
brew install mysql

# Then install the Python MySQL client:
pip install mysqlclient
```

#### 4. Configure the database

The project is configured to use SQLite for development. For production, uncomment the MySQL configuration in `lipsweb/settings.py` and update the credentials.

#### 5. Create and run migrations

```bash
# For all apps
./migrate.sh

# For a specific app
./migrate.sh app.auth_app
```

**Note:** Migration files are not included in the Git repository. You'll need to generate them when setting up the project.

#### 6. Create a superuser

```bash
python manage.py createsuperuser
```
#### 7. demo data create in database

```bash
python run_demo_data.py 
```

#### 8. Run the development server

```bash
python manage.py runserver
python manage.py runserver *************:8000
```

## Database Setup - IMPORTANT
 mysql -u root -p 

You can add these roles using the Django admin interface or directly with SQL.

## API Endpoints

### Authentication

* `POST /api/v1/auth/register` - Register a new user
* `POST /api/v1/auth/login` - Login and get JWT token
* `POST /api/v1/auth/forgot-password` - Request password reset
* `POST /api/v1/auth/reset-password` - Reset password with token
* `POST /api/v1/auth/change-password` - Change password (authenticated)

### User Management

* `GET /api/v1/users/me` - Get current user profile
* `PUT /api/v1/users/me` - Update current user profile

## Authentication

To authenticate API requests, include the access token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## MVC Pattern

This project follows the MVC (Model-View-Controller) pattern:

* **Models**: Defined in `users/models.py` and `app/auth_app/models.py`
* **Views**: Implemented as APIViews in `app/auth_app/views.py` and `users/views.py`
* **Controllers**: Handled by Django REST Framework's serializers in `app/auth_app/serializers.py` and `users/serializers.py`

## Testing

Run the tests with:

```bash
python manage.py test app.auth_app
```

Or use the test script:

```bash
./run_auth_tests.sh
```

## Project Structure

```
web-backend/
├── config/                      # Configuration folder
│   ├── __init__.py              # Init file for the config module
│   ├── asgi.py                  # ASGI configuration
│   ├── settings.py              # Settings for the project
│   ├── urls.py                  # Main URL routing
│   └── wsgi.py                  # WSGI configuration
├── lips/                        # Users app (lips app for user management)
│   ├── __init__.py              # Init file for the lips app
│   ├── models.py                # User models
│   ├── serializers.py           # User serializers
│   ├── urls.py                  # User URL routing
│   └── views.py                 # User views
├── notification/                # Notification app
│   ├── __init__.py              # Init file for notification app
│   ├── models.py                # Notification models
│   ├── serializers.py           # Notification serializers
│   ├── urls.py                  # Notification URL routing
│   └── views.py                 # Notification views
├── usermanagement/              # User management app
│   ├── __init__.py              # Init file for usermanagement app
│   ├── models.py                # Usermanagement models
│   ├── serializers.py           # Usermanagement serializers
│   ├── urls.py                  # Usermanagement URL routing
│   └── views.py                 # Usermanagement views
├── manage.py                    # Django management script
├── README.md                    # Project documentation
├── .gitignore                   # Git ignore file
├── run_auth_tests.sh            # Test runner script
├── migrate.sh                   # Migration helper script
├── test_auth_api.py             # API test client
└── venv/                        # Virtual environment folder
```

## Running the Application / swagger login
 python manage.py collectstatic
### Development Server

```
python manage.py runserver
```

### Creating a Superuser

```
python manage.py createsuperuser
```

## API Documentation

The API documentation is available at:

* Swagger UI: `/` (root URL when server is running)
* ReDoc: `/redoc/`

## Main API Endpoints

* Authentication & User Management: `/api/v1/userapi/`
* LIPS API: `/api/v1/lipsapi/`
* Notification API: `/api/v1/`

## Testing

```
python manage.py test
```

## Troubleshooting

If you encounter any issues with role-based access:

1. Verify the `usermanagement_role` table has the correct role IDs
2. Check that user role assignments are correct in the database
3. Ensure your authentication tokens are properly configured

```
