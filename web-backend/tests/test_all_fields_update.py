#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test that all fields in the DeviceMaster model are updating properly through the API.
This script will:
1. Start the Django development server
2. Get a device from the database
3. Update all fields through the API
4. Verify that all fields were updated correctly in the database
5. Make another API call to get the device settings
6. Verify that all fields in the API response match the updated values
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, WorkInfo, SettingsInfo, UserMaster

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )
    
    # Wait for the server to start
    time.sleep(3)
    
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def get_auth_token():
    """Get an authentication token for API requests"""
    print("Getting authentication token...")
    
    # Create admin user if it doesn't exist
    from django.contrib.auth.hashers import make_password
    from usermanagement.models import User, Role
    
    # Get admin role
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(id=1, name='admin')
    
    # Create admin user if it doesn't exist
    admin_user = User.objects.filter(username='admin').first()
    if not admin_user:
        print("Creating admin user...")
        admin_user = User.objects.create(
            username='admin',
            password=make_password('admin'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Admin User',
            status='active'
        )
    
    # Get token using the API
    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None
    
    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def get_different_work(current_work_id):
    """Get a different work from the database"""
    print("Getting a different work from the database...")
    work = WorkInfo.objects.exclude(id=current_work_id).first()
    if not work:
        print("No other works found in the database. Creating a new one...")
        work = WorkInfo.objects.create(
            work_name="Test Work",
            group_num="TEST",
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    print(f"Found work: ID={work.id}, Name={work.work_name}")
    return work

def get_different_user(current_user_id):
    """Get a different user from the database"""
    print("Getting a different user from the database...")
    user = UserMaster.objects.exclude(id=current_user_id).first()
    if not user:
        print("No other users found in the database. Creating a new one...")
        user = UserMaster.objects.create(
            id="TEST_USER",
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    print(f"Found user: ID={user.id}")
    return user

def print_device_details(device):
    """Print all fields of a device"""
    print("\nDevice details:")
    print(f"ID: {device.id}")
    print(f"User ID: {device.user_id.id if device.user_id else None}")
    print(f"Display Device ID: {device.display_device_id}")
    print(f"Device Name: {device.device_name}")
    print(f"Work ID: {device.work_id.id if device.work_id else None}")
    print(f"Work Time: {device.work_time}")
    print(f"Battery: {device.battery}")
    print(f"Previous Alert Instruction: {device.previous_alert_instruction}")
    print(f"Signal Period: {device.signal_period}")
    print(f"Status: {device.status}")
    print(f"Created At: {device.created_at}")
    print(f"Updated At: {device.updated_at}")

def get_approach_settings(user_id):
    """Get the approach distance and seconds settings from the database"""
    print(f"Getting approach settings from database for user {user_id}...")
    
    # Get approach distance setting
    distance_setting = SettingsInfo.objects.filter(
        key_name='neighborhoodThreshold'
    ).filter(
        id__in=[user_id, f"{user_id}_neighborhoodThreshold"]
    ).first()
    
    # Get approach seconds setting
    seconds_setting = SettingsInfo.objects.filter(
        key_name='estimationSec'
    ).filter(
        id__in=[user_id, f"{user_id}_estimationSec"]
    ).first()
    
    distance_value = float(distance_setting.value) / 1000 if distance_setting else 5.0
    seconds_value = int(seconds_setting.value) if seconds_setting else 30
    
    print(f"  Approach Distance: {distance_value} meters")
    print(f"  Approach Seconds: {seconds_value} seconds")
    
    return {
        'approachDistance': distance_value,
        'approachSeconds': seconds_value
    }

def update_all_fields_api(device_id, update_data, token):
    """Update all fields of a device through the API"""
    print(f"Updating all fields for device ID {device_id} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    try:
        response = requests.put(url, json=update_data, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def get_device_settings_api(device_id, token):
    """Get the device settings through the API"""
    print(f"Getting device settings for device ID {device_id} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def verify_device_update(device_id, expected_values):
    """Verify that all fields were updated correctly in the database"""
    print(f"Verifying database update for device ID {device_id}...")
    
    # Get the device from the database
    device = DeviceMaster.objects.get(id=device_id)
    
    # Check if all fields were updated
    success = True
    discrepancies = []
    
    if 'userId' in expected_values and device.user_id.id != expected_values['userId']:
        discrepancies.append(f"User ID: expected {expected_values['userId']}, got {device.user_id.id}")
        success = False
    
    if 'deviceId' in expected_values and device.display_device_id != expected_values['deviceId']:
        discrepancies.append(f"Display Device ID: expected {expected_values['deviceId']}, got {device.display_device_id}")
        success = False
    
    if 'name' in expected_values and device.device_name != expected_values['name']:
        discrepancies.append(f"Device Name: expected {expected_values['name']}, got {device.device_name}")
        success = False
    
    if 'workId' in expected_values and device.work_id.id != expected_values['workId']:
        discrepancies.append(f"Work ID: expected {expected_values['workId']}, got {device.work_id.id}")
        success = False
    
    if 'workTime' in expected_values and device.work_time != expected_values['workTime']:
        discrepancies.append(f"Work Time: expected {expected_values['workTime']}, got {device.work_time}")
        success = False
    
    if 'battery' in expected_values and device.battery != expected_values['battery']:
        discrepancies.append(f"Battery: expected {expected_values['battery']}, got {device.battery}")
        success = False
    
    if 'previousAlertInstruction' in expected_values and device.previous_alert_instruction != expected_values['previousAlertInstruction']:
        discrepancies.append(f"Previous Alert Instruction: expected {expected_values['previousAlertInstruction']}, got {device.previous_alert_instruction}")
        success = False
    
    if 'signalPeriod' in expected_values and device.signal_period != expected_values['signalPeriod']:
        discrepancies.append(f"Signal Period: expected {expected_values['signalPeriod']}, got {device.signal_period}")
        success = False
    
    if 'status' in expected_values and device.status != expected_values['status']:
        discrepancies.append(f"Status: expected {expected_values['status']}, got {device.status}")
        success = False
    
    # Check approach settings
    if device.user_id:
        approach_settings = get_approach_settings(device.user_id.id)
        
        if 'approachDistance' in expected_values and abs(approach_settings['approachDistance'] - expected_values['approachDistance']) > 0.001:
            discrepancies.append(f"Approach Distance: expected {expected_values['approachDistance']}, got {approach_settings['approachDistance']}")
            success = False
        
        if 'approachSeconds' in expected_values and approach_settings['approachSeconds'] != expected_values['approachSeconds']:
            discrepancies.append(f"Approach Seconds: expected {expected_values['approachSeconds']}, got {approach_settings['approachSeconds']}")
            success = False
    
    if success:
        print("All fields were updated correctly in the database!")
    else:
        print("Some fields were not updated correctly in the database:")
        for discrepancy in discrepancies:
            print(f"  - {discrepancy}")
    
    return success

def verify_api_response(response_data, expected_values):
    """Verify that all fields in the API response match the expected values"""
    print("Verifying API response...")
    
    # Extract the data from the response
    data = response_data.get('data', {})
    
    # Check if all fields match the expected values
    success = True
    discrepancies = []
    
    if 'userId' in expected_values and data.get('userId') != expected_values['userId']:
        discrepancies.append(f"User ID: expected {expected_values['userId']}, got {data.get('userId')}")
        success = False
    
    if 'deviceId' in expected_values and data.get('deviceId') != expected_values['deviceId']:
        discrepancies.append(f"Device ID: expected {expected_values['deviceId']}, got {data.get('deviceId')}")
        success = False
    
    if 'name' in expected_values and data.get('name') != expected_values['name']:
        discrepancies.append(f"Name: expected {expected_values['name']}, got {data.get('name')}")
        success = False
    
    if 'workId' in expected_values and data.get('workId') != expected_values['workId']:
        discrepancies.append(f"Work ID: expected {expected_values['workId']}, got {data.get('workId')}")
        success = False
    
    if 'workTime' in expected_values and data.get('workTime') != expected_values['workTime']:
        discrepancies.append(f"Work Time: expected {expected_values['workTime']}, got {data.get('workTime')}")
        success = False
    
    if 'battery' in expected_values and data.get('battery') != expected_values['battery']:
        discrepancies.append(f"Battery: expected {expected_values['battery']}, got {data.get('battery')}")
        success = False
    
    if 'signalPeriod' in expected_values and data.get('signalPeriod') != expected_values['signalPeriod']:
        discrepancies.append(f"Signal Period: expected {expected_values['signalPeriod']}, got {data.get('signalPeriod')}")
        success = False
    
    if 'approachDistance' in expected_values and abs(data.get('approachDistance', 0) - expected_values['approachDistance']) > 0.001:
        discrepancies.append(f"Approach Distance: expected {expected_values['approachDistance']}, got {data.get('approachDistance')}")
        success = False
    
    if 'approachSeconds' in expected_values and data.get('approachSeconds') != expected_values['approachSeconds']:
        discrepancies.append(f"Approach Seconds: expected {expected_values['approachSeconds']}, got {data.get('approachSeconds')}")
        success = False
    
    if success:
        print("All fields in the API response match the expected values!")
    else:
        print("Some fields in the API response do not match the expected values:")
        for discrepancy in discrepancies:
            print(f"  - {discrepancy}")
    
    return success

def restore_device(device, original_values):
    """Restore the device to its original values"""
    print("\nRestoring device to original values...")
    
    # Restore all fields
    device.user_id = original_values['user_id']
    device.display_device_id = original_values['display_device_id']
    device.device_name = original_values['device_name']
    device.work_id = original_values['work_id']
    device.work_time = original_values['work_time']
    device.battery = original_values['battery']
    device.previous_alert_instruction = original_values['previous_alert_instruction']
    device.signal_period = original_values['signal_period']
    device.status = original_values['status']
    device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Save the device
    device.save()
    
    print("Device restored successfully.")

def main():
    """Main function"""
    print("=== All Fields Update Test ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Get authentication token
        token = get_auth_token()
        if not token:
            print("Failed to get authentication token.")
            return 1
        
        # Get a device from the database
        device = get_device()
        if not device:
            print("No device to test with.")
            return 1
        
        # Print device details
        print_device_details(device)
        
        # Store original values
        original_values = {
            'user_id': device.user_id,
            'display_device_id': device.display_device_id,
            'device_name': device.device_name,
            'work_id': device.work_id,
            'work_time': device.work_time,
            'battery': device.battery,
            'previous_alert_instruction': device.previous_alert_instruction,
            'signal_period': device.signal_period,
            'status': device.status,
        }
        
        # Get a different work and user for updating
        current_work_id = device.work_id.id if device.work_id else None
        current_user_id = device.user_id.id if device.user_id else None
        
        different_work = get_different_work(current_work_id)
        different_user = get_different_user(current_user_id)
        
        # Prepare update data
        update_data = {
            'name': f"{device.device_name}_updated",
            'deviceId': f"{device.display_device_id}_updated",
            'userId': different_user.id,
            'workId': different_work.id,
            'workTime': "12:34:56",
            'battery': 77 if device.battery != 77 else 78,
            'previousAlertInstruction': "updated_instruction",
            'signalPeriod': 150 if device.signal_period != 150 else 160,
            'status': "Area Change" if device.status != "Area Change" else "No Alert",
            'approachDistance': 7.5,  # Required field
            'approachSeconds': 45,    # Required field
        }
        
        # Update all fields through the API
        update_response = update_all_fields_api(device.id, update_data, token)
        if not update_response:
            print("Failed to update device through API.")
            return 1
        
        # Verify that all fields were updated correctly in the database
        db_success = verify_device_update(device.id, update_data)
        
        # Make another API call to get the device settings
        get_response = get_device_settings_api(device.id, token)
        if not get_response:
            print("Failed to get device settings through API.")
            return 1
        
        # Verify that all fields in the API response match the updated values
        api_success = verify_api_response(get_response, update_data)
        
        # Restore the device to its original values
        restore_device(device, original_values)
        
        # Print restored device details
        print_device_details(device)
        
        if db_success and api_success:
            print("\nTest completed successfully!")
            return 0
        else:
            print("\nTest failed.")
            return 1
    
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
