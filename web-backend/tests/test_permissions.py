#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test the permission system.
This script will:
1. Start the Django development server
2. Create test users with different roles (admin, manager, regular user)
3. Test access to various endpoints with different users
4. Verify that the permission system is working correctly
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, WorkInfo, SettingsInfo, UserMaster
from django.contrib.auth.hashers import make_password
from usermanagement.models import User, Role

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )

    # Wait for the server to start
    time.sleep(3)

    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def create_test_users():
    """Create test users with different roles"""
    print("Creating test users...")

    # Create roles if they don't exist
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(name='admin')

    manager_role = Role.objects.filter(name='manager').first()
    if not manager_role:
        print("Creating manager role...")
        manager_role = Role.objects.create(name='manager')

    user_role = Role.objects.filter(name='user').first()
    if not user_role:
        print("Creating user role...")
        user_role = Role.objects.create(name='user')

    # Print roles for debugging
    print(f"Admin role ID: {admin_role.id}, Name: {admin_role.name}")
    print(f"Manager role ID: {manager_role.id}, Name: {manager_role.name}")
    print(f"User role ID: {user_role.id}, Name: {user_role.name}")

    # Create test users
    users = {
        'admin_user': {
            'username': 'test_admin',
            'password': 'test_password',
            'email': '<EMAIL>',
            'role': admin_role,
            'full_name': 'Test Admin',
            'status': 'active'
        },
        'manager_user': {
            'username': 'test_manager',
            'password': 'test_password',
            'email': '<EMAIL>',
            'role': manager_role,
            'full_name': 'Test Manager',
            'status': 'active'
        },
        'regular_user': {
            'username': 'test_user',
            'password': 'test_password',
            'email': '<EMAIL>',
            'role': user_role,
            'full_name': 'Test User',
            'status': 'active'
        }
    }

    created_users = {}

    for user_type, user_data in users.items():
        # Create user if it doesn't exist
        user = User.objects.filter(username=user_data['username']).first()
        if not user:
            print(f"Creating {user_type}...")
            try:
                user = User.objects.create(
                    username=user_data['username'],
                    password=make_password(user_data['password']),
                    email=user_data['email'],
                    role=user_data['role'],
                    full_name=user_data['full_name'],
                    status=user_data['status']
                )
                print(f"Created user {user.username} with role {user.role.name}")
            except django.db.utils.IntegrityError as e:
                print(f"Error creating user {user_data['username']}: {e}")
                # Try to get the user by username
                user = User.objects.filter(username=user_data['username']).first()
                if not user:
                    # Try to get the user by email
                    user = User.objects.filter(email=user_data['email']).first()
                    if not user:
                        print(f"Could not create or find user {user_data['username']}")
                        continue
        else:
            print(f"User {user.username} already exists with role {user.role.name if user.role else 'None'}")
            # Update the role if needed
            if user.role != user_data['role']:
                print(f"Updating role for {user.username} from {user.role.name if user.role else 'None'} to {user_data['role'].name}")
                user.role = user_data['role']
                user.save()

        # Create UserMaster record if it doesn't exist
        user_master = UserMaster.objects.filter(id=user_data['username']).first()
        if not user_master:
            print(f"Creating UserMaster record for {user_type}...")
            user_master = UserMaster.objects.create(
                id=user_data['username'],
                created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
                updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
            )

        created_users[user_type] = user_data

    print("Test users created successfully.")
    return created_users

def get_auth_token(username, password):
    """Get an authentication token for API requests"""
    print(f"Getting authentication token for user {username}...")

    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": username,
        "password": password
    }

    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def test_endpoint_access(endpoint, method, data, token, expected_status):
    """Test access to an endpoint with a specific user"""
    print(f"Testing {method} access to {endpoint} with token...")

    url = f"http://localhost:8000/api/v1/lipsapi/{endpoint}"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}' if token else ''
    }

    # Print token for debugging
    print(f"Using token: {token[:20]}...")

    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        elif method == 'DELETE':
            response = requests.delete(url, headers=headers)
        else:
            print(f"Unsupported method: {method}")
            return False

        print(f"Response status code: {response.status_code}")

        # Print response content for debugging
        print(f"Response content: {response.text}")

        if response.status_code == expected_status:
            print(f"Success! Got expected status code {expected_status}")
            return True
        else:
            print(f"Failure! Expected status code {expected_status}, got {response.status_code}")
            return False
    except Exception as e:
        print(f"Error sending request: {e}")
        return False

def main():
    """Main function"""
    print("=== Permission System Test ===")

    # Start the server
    server_process = start_server()

    try:
        # Create test users
        users = create_test_users()

        # Get authentication tokens
        tokens = {}
        for user_type, user_data in users.items():
            token = get_auth_token(user_data['username'], user_data['password'])
            if token:
                tokens[user_type] = token
            else:
                print(f"Failed to get token for {user_type}.")
                return 1

        # Test endpoints
        test_cases = [
            # Regular user can view devices but not modify them
            {
                'user_type': 'regular_user',
                'endpoint': 'devices',
                'method': 'GET',
                'data': None,
                'expected_status': 200
            },
            {
                'user_type': 'regular_user',
                'endpoint': 'device/settings/1/',
                'method': 'PUT',
                'data': {
                    'name': 'Updated Device',
                    'approachDistance': 10.0,
                    'approachSeconds': 60,
                    'approachAreaDistance': 15.0,
                    'approachAreaSeconds': 90
                },
                'expected_status': 403  # Forbidden
            },
            # Admin user can view and modify devices
            {
                'user_type': 'admin_user',
                'endpoint': 'devices',
                'method': 'GET',
                'data': None,
                'expected_status': 200
            },
            {
                'user_type': 'admin_user',
                'endpoint': 'device/settings/1/',
                'method': 'PUT',
                'data': {
                    'name': 'Admin Updated Device',
                    'approachDistance': 10.0,
                    'approachSeconds': 60,
                    'approachAreaDistance': 15.0,
                    'approachAreaSeconds': 90
                },
                'expected_status': 200
            },
            # Manager user can view and modify devices
            {
                'user_type': 'manager_user',
                'endpoint': 'devices',
                'method': 'GET',
                'data': None,
                'expected_status': 200
            },
            {
                'user_type': 'manager_user',
                'endpoint': 'device/settings/1/',
                'method': 'PUT',
                'data': {
                    'name': 'Manager Updated Device',
                    'approachDistance': 10.0,
                    'approachSeconds': 60,
                    'approachAreaDistance': 15.0,
                    'approachAreaSeconds': 90
                },
                'expected_status': 200
            }
        ]

        results = []
        for test_case in test_cases:
            user_type = test_case['user_type']
            endpoint = test_case['endpoint']
            method = test_case['method']
            data = test_case['data']
            expected_status = test_case['expected_status']

            print(f"\nTesting {method} {endpoint} as {user_type}...")
            result = test_endpoint_access(endpoint, method, data, tokens[user_type], expected_status)
            results.append(result)

        # Print summary
        print("\nTest Results Summary:")
        for i, test_case in enumerate(test_cases):
            user_type = test_case['user_type']
            endpoint = test_case['endpoint']
            method = test_case['method']
            expected_status = test_case['expected_status']
            result = results[i]

            print(f"{user_type} {method} {endpoint}: {'PASS' if result else 'FAIL'}")

        # Check if all tests passed
        if all(results):
            print("\nAll permission tests passed!")
            return 0
        else:
            print("\nSome permission tests failed.")
            return 1

    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
