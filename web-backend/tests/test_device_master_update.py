#!/usr/bin/env python
"""
<PERSON>ript to test updating all fields of the DeviceMaster model.
This script will:
1. Get a device from the database
2. Update all fields of the device
3. Verify that all fields were updated correctly
"""

import os
import sys
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, WorkInfo, UserMaster

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None

    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def get_different_work(current_work_id):
    """Get a different work from the database"""
    print("Getting a different work from the database...")
    work = WorkInfo.objects.exclude(id=current_work_id).first()
    if not work:
        print("No other works found in the database. Creating a new one...")
        work = WorkInfo.objects.create(
            work_name="Test Work",
            group_num="TEST",
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    print(f"Found work: ID={work.id}, Name={work.work_name}")
    return work

def get_different_user(current_user_id):
    """Get a different user from the database"""
    print("Getting a different user from the database...")
    user = UserMaster.objects.exclude(id=current_user_id).first()
    if not user:
        print("No other users found in the database. Creating a new one...")
        user = UserMaster.objects.create(
            id="TEST_USER",
            created_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        )

    print(f"Found user: ID={user.id}")
    return user

def print_device_details(device):
    """Print all fields of a device"""
    print("\nDevice details:")
    print(f"ID: {device.id}")
    print(f"User ID: {device.user_id.id if device.user_id else None}")
    print(f"Display Device ID: {device.display_device_id}")
    print(f"Device Name: {device.device_name}")
    print(f"Work ID: {device.work_id.id if device.work_id else None}")
    print(f"Work Time: {device.work_time}")
    print(f"Battery: {device.battery}")
    print(f"Previous Alert Instruction: {device.previous_alert_instruction}")
    print(f"Signal Period: {device.signal_period}")
    print(f"Status: {device.status}")
    print(f"Created At: {device.created_at}")
    print(f"Updated At: {device.updated_at}")

def update_device_all_fields(device):
    """Update all fields of a device"""
    print("\nUpdating all fields of the device...")

    # Get a different work and user for updating
    current_work_id = device.work_id.id if device.work_id else None
    current_user_id = device.user_id.id if device.user_id else None

    work = get_different_work(current_work_id)
    user = get_different_user(current_user_id)

    # Store original values
    original_values = {
        'user_id': device.user_id,
        'display_device_id': device.display_device_id,
        'device_name': device.device_name,
        'work_id': device.work_id,
        'work_time': device.work_time,
        'battery': device.battery,
        'previous_alert_instruction': device.previous_alert_instruction,
        'signal_period': device.signal_period,
        'status': device.status,
    }

    # Update all fields with new values
    device.user_id = user
    device.display_device_id = f"{device.display_device_id}_updated"
    device.device_name = f"{device.device_name}_updated"
    device.work_id = work
    device.work_time = "12:34:56"
    device.battery = 99 if device.battery != 99 else 98
    device.previous_alert_instruction = "updated_instruction"
    device.signal_period = 120 if device.signal_period != 120 else 90
    device.status = "No Alert" if device.status != "No Alert" else "Area Change"
    device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')

    # Save the device
    device.save()

    print("Device updated successfully.")
    return original_values

def verify_device_update(device, original_values):
    """Verify that all fields were updated correctly"""
    print("\nVerifying device update...")

    # Refresh the device from the database
    device.refresh_from_db()

    # Check if all fields were updated
    success = True
    if device.user_id == original_values['user_id']:
        print(f"User ID was not updated: {device.user_id}")
        success = False

    if device.display_device_id == original_values['display_device_id']:
        print(f"Display Device ID was not updated: {device.display_device_id}")
        success = False

    if device.device_name == original_values['device_name']:
        print(f"Device Name was not updated: {device.device_name}")
        success = False

    if device.work_id == original_values['work_id']:
        print(f"Work ID was not updated: {device.work_id}")
        success = False

    if device.work_time == original_values['work_time']:
        print(f"Work Time was not updated: {device.work_time}")
        success = False

    if device.battery == original_values['battery']:
        print(f"Battery was not updated: {device.battery}")
        success = False

    if device.previous_alert_instruction == original_values['previous_alert_instruction']:
        print(f"Previous Alert Instruction was not updated: {device.previous_alert_instruction}")
        success = False

    if device.signal_period == original_values['signal_period']:
        print(f"Signal Period was not updated: {device.signal_period}")
        success = False

    if device.status == original_values['status']:
        print(f"Status was not updated: {device.status}")
        success = False

    if success:
        print("All fields were updated successfully!")
    else:
        print("Some fields were not updated correctly.")

    return success

def restore_device(device, original_values):
    """Restore the device to its original values"""
    print("\nRestoring device to original values...")

    # Restore all fields
    device.user_id = original_values['user_id']
    device.display_device_id = original_values['display_device_id']
    device.device_name = original_values['device_name']
    device.work_id = original_values['work_id']
    device.work_time = original_values['work_time']
    device.battery = original_values['battery']
    device.previous_alert_instruction = original_values['previous_alert_instruction']
    device.signal_period = original_values['signal_period']
    device.status = original_values['status']
    device.updated_at = timezone.now().strftime('%Y-%m-%d %H:%M:%S')

    # Save the device
    device.save()

    print("Device restored successfully.")

def main():
    """Main function"""
    print("=== DeviceMaster Update Test ===")

    # Get a device from the database
    device = get_device()
    if not device:
        print("No device to test with.")
        return 1

    # Print device details
    print_device_details(device)

    # Update all fields of the device
    original_values = update_device_all_fields(device)

    # Print updated device details
    print_device_details(device)

    # Verify that all fields were updated correctly
    success = verify_device_update(device, original_values)

    # Restore the device to its original values
    restore_device(device, original_values)

    # Print restored device details
    print_device_details(device)

    if success:
        print("\nTest completed successfully!")
        return 0
    else:
        print("\nTest failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
