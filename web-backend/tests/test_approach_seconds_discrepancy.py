#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test for discrepancies between the approach seconds value in the API response
and the actual value stored in the database.

This script will:
1. Start the Django development server
2. Make multiple API calls to update the approach seconds
3. After each call, check the response value and the database value
4. Report any discrepancies
"""

import os
import sys
import json
import time
import requests
import subprocess
import signal
import django
from django.utils import timezone
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, SettingsInfo, UserMaster

def start_server():
    """Start the Django development server"""
    print("Starting Django development server...")
    server_process = subprocess.Popen(
        ["python", "manage.py", "runserver", "8000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        preexec_fn=os.setsid  # Use this to create a new process group
    )
    
    # Wait for the server to start
    time.sleep(3)
    
    return server_process

def stop_server(server_process):
    """Stop the Django development server"""
    print("Stopping Django development server...")
    try:
        os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
        server_process.wait()
    except ProcessLookupError:
        print("Process already terminated.")

def get_auth_token():
    """Get an authentication token for API requests"""
    print("Getting authentication token...")
    
    # Create admin user if it doesn't exist
    from django.contrib.auth.hashers import make_password
    from usermanagement.models import User, Role
    
    # Get admin role
    admin_role = Role.objects.filter(name='admin').first()
    if not admin_role:
        print("Creating admin role...")
        admin_role = Role.objects.create(id=1, name='admin')
    
    # Create admin user if it doesn't exist
    admin_user = User.objects.filter(username='admin').first()
    if not admin_user:
        print("Creating admin user...")
        admin_user = User.objects.create(
            username='admin',
            password=make_password('admin'),
            email='<EMAIL>',
            role=admin_role,
            full_name='Admin User',
            status='active'
        )
    
    # Get token using the API
    url = "http://localhost:8000/api/v1/userapi/token/"
    data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token_data = response.json()
            print("Authentication token obtained successfully.")
            return token_data.get('access')
        else:
            print(f"Failed to get authentication token: {response.text}")
            return None
    except Exception as e:
        print(f"Error getting authentication token: {e}")
        return None

def get_device():
    """Get a device from the database"""
    print("Getting a device from the database...")
    device = DeviceMaster.objects.first()
    if not device:
        print("No devices found in the database.")
        return None
    
    print(f"Found device: ID={device.id}, Name={device.device_name}")
    return device

def get_approach_seconds_from_db(user_id):
    """Get the approach seconds setting from the database"""
    print(f"Getting approach seconds from database for user {user_id}...")
    
    # Get estimationSec setting with user ID
    setting_with_user_id = SettingsInfo.objects.filter(
        id=user_id,
        key_name='estimationSec'
    ).first()
    
    # Get estimationSec setting with user ID prefix
    setting_with_prefix = SettingsInfo.objects.filter(
        id=f"{user_id}_estimationSec",
        key_name='estimationSec'
    ).first()
    
    if setting_with_user_id:
        value = int(setting_with_user_id.value)
        print(f"  Setting with user ID: {setting_with_user_id.id}, Value: {value}")
        return value
    
    if setting_with_prefix:
        value = int(setting_with_prefix.value)
        print(f"  Setting with user ID prefix: {setting_with_prefix.id}, Value: {value}")
        return value
    
    print("  No setting found.")
    return None

def update_approach_seconds_api(device_id, new_value, token):
    """Update the approach seconds setting through the API"""
    print(f"Updating approach seconds setting for device ID {device_id} to {new_value} through the API...")
    
    url = f"http://localhost:8000/api/v1/lipsapi/device/settings/{device_id}/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    # Get the device to get the current values
    device = DeviceMaster.objects.get(id=device_id)
    
    # Prepare the update data
    update_data = {
        'name': device.device_name,
        'deviceId': device.display_device_id,
        'signalPeriod': device.signal_period,
        'approachDistance': 5.0,  # Default value
        'approachSeconds': new_value
    }
    
    try:
        response = requests.put(url, json=update_data, headers=headers)
        print(f"Response status code: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("Response data:")
            pprint(response_data)
            return response_data
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Error sending request: {e}")
        return None

def check_for_discrepancy(user_id, expected_value, response_data):
    """Check for discrepancies between the API response and the database value"""
    print(f"Checking for discrepancies for user {user_id}...")
    
    # Get the value from the API response
    response_value = response_data.get('data', {}).get('approachSeconds')
    print(f"  Value from API response: {response_value}")
    
    # Get the value from the database
    db_value = get_approach_seconds_from_db(user_id)
    print(f"  Value from database: {db_value}")
    
    # Check for discrepancies
    discrepancies = []
    
    if response_value != expected_value:
        discrepancies.append(f"API response value ({response_value}) does not match expected value ({expected_value})")
    
    if db_value != expected_value:
        discrepancies.append(f"Database value ({db_value}) does not match expected value ({expected_value})")
    
    if response_value != db_value:
        discrepancies.append(f"API response value ({response_value}) does not match database value ({db_value})")
    
    if discrepancies:
        print("  Discrepancies found:")
        for discrepancy in discrepancies:
            print(f"    - {discrepancy}")
        return False
    else:
        print("  No discrepancies found.")
        return True

def run_sequential_tests(device_id, user_id, token, num_tests=5):
    """Run multiple sequential tests to check for discrepancies"""
    print(f"Running {num_tests} sequential tests...")
    
    # Get the initial value
    initial_value = get_approach_seconds_from_db(user_id)
    if initial_value is None:
        print("No initial value found.")
        return False
    
    # Store test results
    test_results = []
    
    # Run the tests
    current_value = initial_value
    for i in range(num_tests):
        print(f"\nTest {i+1}/{num_tests}")
        
        # Calculate a new value
        new_value = current_value + 5
        
        # Update the approach seconds setting through the API
        response_data = update_approach_seconds_api(device_id, new_value, token)
        if not response_data:
            print("Failed to update approach seconds through API.")
            test_results.append(False)
            continue
        
        # Check for discrepancies
        success = check_for_discrepancy(user_id, new_value, response_data)
        test_results.append(success)
        
        # Update the current value
        current_value = new_value
        
        # Add a small delay to ensure the database is updated
        time.sleep(1)
    
    # Restore the initial value
    print("\nRestoring initial value...")
    restore_response = update_approach_seconds_api(device_id, initial_value, token)
    if not restore_response:
        print("Failed to restore initial value.")
        return False
    
    # Check if all tests passed
    all_passed = all(test_results)
    if all_passed:
        print("\nAll tests passed!")
    else:
        print("\nSome tests failed!")
        for i, result in enumerate(test_results):
            print(f"  Test {i+1}: {'Passed' if result else 'Failed'}")
    
    return all_passed

def main():
    """Main function"""
    print("=== Approach Seconds Discrepancy Test ===")
    
    # Start the server
    server_process = start_server()
    
    try:
        # Get authentication token
        token = get_auth_token()
        if not token:
            print("Failed to get authentication token.")
            return 1
        
        # Get a device from the database
        device = get_device()
        if not device:
            print("No device to test with.")
            return 1
        
        # Get the user ID
        user_id = device.user_id.id if device.user_id else None
        if not user_id:
            print("Device has no user ID.")
            return 1
        
        # Run sequential tests
        success = run_sequential_tests(device.id, user_id, token)
        
        if success:
            print("\nTest completed successfully!")
            return 0
        else:
            print("\nTest failed.")
            return 1
    
    finally:
        # Stop the server
        stop_server(server_process)

if __name__ == "__main__":
    sys.exit(main())
