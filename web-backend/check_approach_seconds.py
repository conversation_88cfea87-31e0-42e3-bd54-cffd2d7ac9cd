#!/usr/bin/env python
"""
Script to check the approach seconds setting in the database.
"""

import os
import sys
import django
from pprint import pprint

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Import models after Django setup
from lips.models import DeviceMaster, SettingsInfo, UserMaster

def check_approach_seconds():
    """Check the approach seconds setting for all users"""
    print("Checking approach seconds setting for all users...")
    
    # Get all users
    users = UserMaster.objects.all()
    
    for user in users:
        print(f"\nUser ID: {user.id}")
        
        # Get estimationSec setting with user ID
        setting_with_user_id = SettingsInfo.objects.filter(
            id=user.id,
            key_name='estimationSec'
        ).first()
        
        if setting_with_user_id:
            print(f"  Setting with user ID: {setting_with_user_id.id}, Value: {setting_with_user_id.value}")
        else:
            print("  No setting with user ID found.")
        
        # Get estimationSec setting with user ID prefix
        setting_with_prefix = SettingsInfo.objects.filter(
            id=f"{user.id}_estimationSec",
            key_name='estimationSec'
        ).first()
        
        if setting_with_prefix:
            print(f"  Setting with user ID prefix: {setting_with_prefix.id}, Value: {setting_with_prefix.value}")
        else:
            print("  No setting with user ID prefix found.")
        
        # Get devices for this user
        devices = DeviceMaster.objects.filter(user_id=user)
        
        print(f"  Devices for this user: {len(devices)}")
        for device in devices:
            print(f"    Device ID: {device.id}, Name: {device.device_name}, Signal Period: {device.signal_period}")

def update_approach_seconds(user_id, new_value):
    """Update the approach seconds setting for a user"""
    print(f"Updating approach seconds setting for user {user_id} to {new_value}...")
    
    # Get user
    user = UserMaster.objects.get(id=user_id)
    
    # Get estimationSec setting with user ID
    setting_with_user_id = SettingsInfo.objects.filter(
        id=user.id,
        key_name='estimationSec'
    ).first()
    
    # Get estimationSec setting with user ID prefix
    setting_with_prefix = SettingsInfo.objects.filter(
        id=f"{user.id}_estimationSec",
        key_name='estimationSec'
    ).first()
    
    # Update the setting
    if setting_with_user_id:
        setting_with_user_id.value = str(new_value)
        setting_with_user_id.save()
        print(f"  Updated setting with user ID: {setting_with_user_id.id}, New value: {setting_with_user_id.value}")
    
    if setting_with_prefix:
        setting_with_prefix.value = str(new_value)
        setting_with_prefix.save()
        print(f"  Updated setting with user ID prefix: {setting_with_prefix.id}, New value: {setting_with_prefix.value}")
    
    if not setting_with_user_id and not setting_with_prefix:
        print("  No settings found to update.")

def main():
    """Main function"""
    # Check current approach seconds settings
    check_approach_seconds()
    
    # Ask if user wants to update a setting
    user_input = input("\nDo you want to update an approach seconds setting? (y/n): ")
    
    if user_input.lower() == 'y':
        user_id = input("Enter user ID: ")
        new_value = input("Enter new value: ")
        
        try:
            update_approach_seconds(user_id, new_value)
            
            # Check updated settings
            check_approach_seconds()
        except Exception as e:
            print(f"Error updating setting: {e}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
