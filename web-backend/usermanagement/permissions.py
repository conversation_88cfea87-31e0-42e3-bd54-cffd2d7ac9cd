from rest_framework import permissions

class IsAdmin(permissions.BasePermission):
    """
    Custom permission to only allow admin users to access the view.
    """
    message = "Only admin users are allowed to perform this action."

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user has a role
        if not request.user.role:
            return False

        # Check if the user is a superuser
        if request.user.is_superuser:
            return True

        # Check if the user's role is admin
        return request.user.role.name == 'admin'

class IsManager(permissions.BasePermission):
    """
    Custom permission to only allow manager users to access the view.
    """
    message = "Only manager users are allowed to perform this action."

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user has a role
        if not request.user.role:
            return False

        # Check if the user's role is manager
        return request.user.role.name == 'manager'

class IsAdminOrManager(permissions.BasePermission):
    """
    Custom permission to allow either admin, manager, superuser, or staff users to access the view.
    """
    message = "Only admin, manager, superuser, or staff users are allowed to perform this action."

    def has_permission(self, request, view):
        # Check if the user is authenticated
        if not request.user.is_authenticated:
            return False

        # Check if the user is a superuser or staff
        if request.user.is_superuser or request.user.is_staff:
            return True

        # Check if the user has a role
        if not request.user.role:
            return False

        # Check if the user's role is admin or manager
        return request.user.role.name in ['admin', 'manager']

class ReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow read-only access.
    """
    def has_permission(self, request, view):
        return request.method in permissions.SAFE_METHODS

class IsAdminOrManagerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to allow read-only access to authenticated users,
    but only allow write access to admin or manager users.
    """
    message = "Only admin or manager users are allowed to modify data."

    def has_permission(self, request, view):
        # Allow read-only access to authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated

        # Check if the user is a superuser or staff
        if request.user.is_superuser or request.user.is_staff:
            return True

        # Check if the user has a role
        if not request.user.role:
            return False

        # Check if the user's role is admin or manager for write operations
        return request.user.role.name in ['admin', 'manager']

    def has_object_permission(self, request, view, obj):
        # Allow read-only access to authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated

        # Check if the user is a superuser or staff
        if request.user.is_superuser or request.user.is_staff:
            return True

        # Check if the user has a role
        if not request.user.role:
            return False

        # Check if the user's role is admin or manager for write operations
        return request.user.role.name in ['admin', 'manager']
