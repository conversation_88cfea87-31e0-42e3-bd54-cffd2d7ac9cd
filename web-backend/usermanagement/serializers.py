import random

from django.contrib.auth import authenticate
from django.contrib.auth.hashers import make_password
from django.utils.timezone import now
from rest_framework import serializers

from .models import Role, User


class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    role = RoleSerializer(read_only=True)
    role_name = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'role',
            'role_name',  
            'full_name',
            'status',
            'phone_number',
            'notes',
            'company_name',
        ]
    
    def update(self, instance, validated_data):
        # Handle role update by name if provided
        role_name = validated_data.pop('role_name', None)
        if role_name:
            try:
                # Find the role with this name and connect user to it
                role = Role.objects.get(name=role_name)
                instance.role = role
                instance.save(update_fields=['role'])
            except Role.DoesNotExist as exc:
                raise serializers.ValidationError(
                    {"role_name": f"Role '{role_name}' not found. Available roles: LimitedEdit<PERSON>, Ad<PERSON>, Editor"}
                ) from exc
        
        # Update other user fields
        return super().update(instance, validated_data)


class UserCreateSerializer(serializers.ModelSerializer):
    # full_name = serializers.CharField(required=False)
    class Meta:
        model = User
        fields = [
            'id',
            'username',
            'email',
            'password',
            'role',
            'full_name',
            'status',
            'phone_number',
            'notes',
            'company_name'
            ]
        extra_kwargs = {'password': {'write_only': True}}

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise serializers.ValidationError("User with this email does not exist.")
        return value


class ResetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField(max_length=6)
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            user = User.objects.get(email=data["email"])
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "Invalid email address."})

        if user.otp != data["otp"]:
            raise serializers.ValidationError({"otp": "Invalid OTP."})

        if user.otp_expired_at and user.otp_expired_at < now():
            raise serializers.ValidationError({"otp": "OTP has expired."})

        return data


class ChangePasswordSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)

    def validate(self, data):
        try:
            user = User.objects.get(email=data["email"])
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "User with this email does not exist."})

        if not user.check_password(data["password"]):
            raise serializers.ValidationError({"password": "Current password is incorrect."})

        return data




class AddUserSerializer(serializers.ModelSerializer):
    role_id = serializers.PrimaryKeyRelatedField(
        queryset=Role.objects.all(), source='role', write_only=True, required=False
    )  # Accept role ID but optional

    class Meta:
        model = User
        fields = [
            "id", "username", "email", "password", "full_name", "status",
            "phone_number", "notes", "company_name", "role", "role_id",
            "otp", "otp_expired_at",
        ]
        extra_kwargs = {
            "password": {"write_only": True},  # Hide password in response
            # "created_at": {"read_only": True},
            # "updated_at": {"read_only": True},
        }

    def create(self, validated_data):
        validated_data["password"] = make_password(validated_data["password"])  # Hash password
        return super().create(validated_data)