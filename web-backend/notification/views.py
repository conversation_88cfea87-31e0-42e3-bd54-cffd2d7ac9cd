from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from lips.models import (
    DeviceMaster,
    DeviceReceiveData,
    HistoricalJudgeAlertInstruction,
    PermitedApproachInfo,
    ProhibitedApproachInfo,
    WorkInfo
)
from .serializers import (
    UserSerializer, DeviceSerializer, DeviceSettingSerializer,
    DeviceAssignmentSerializer, WorkSerializer, WorkGroupSerializer,
    AlertSerializer
)
from usermanagement.permissions import IsAd<PERSON>, IsManager, IsAdminOrManager

User = get_user_model()

# User Views
class UserProfileView(APIView):
    permission_classes = [IsAdmin]  # Only admin can access user profiles

    def get(self, request):
        """Get current user profile"""
        serializer = UserSerializer(request.user)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def put(self, request):
        """Update current user profile"""
        serializer = UserSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'プロフィールが更新されました',
                'data': serializer.data
            })
        return Response({
            'success': False,
            'message': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

# Device Views
class DeviceViewSet(viewsets.ViewSet):
    """ViewSet for DeviceMaster model"""
    permission_classes = [IsManager]  # Only manager can manage devices

    def list(self, request):
        """Get all devices"""
        devices = DeviceMaster.objects.all()
        serializer = DeviceSerializer(devices, many=True)
        return Response({
            'success': True,
            'data': {
                'devices': serializer.data
            }
        })

    def retrieve(self, request, pk=None):
        """Get device by ID"""
        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = DeviceSerializer(device)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['put'])
    def assign(self, request, pk=None):
        """Assign work to device"""
        try:
            device = DeviceMaster.objects.get(pk=pk)
            serializer = DeviceAssignmentSerializer(data=request.data)

            if serializer.is_valid():
                work_name = serializer.validated_data.get('assigned_work')

                if work_name:
                    try:
                        # Just update the work_id field
                        device.work_id = work_name
                    except Exception as e:
                        return Response({
                            'success': False,
                            'message': f"Error assigning work: {str(e)}"
                        }, status=status.HTTP_400_BAD_REQUEST)
                else:
                    device.work_id = None

                device.save()
                return Response({
                    'success': True,
                    'message': 'デバイスの割り当てが更新されました',
                    'data': DeviceSerializer(device).data
                })

            return Response({
                'success': False,
                'message': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

class DeviceSettingsViewSet(viewsets.ViewSet):
    """ViewSet for device settings"""
    permission_classes = [IsManager]  # Only manager can manage device settings

    def list(self, request):
        """Get all device settings"""
        devices = DeviceMaster.objects.all()
        device_settings = []

        for device in devices:
            device_data = {
                'id': device.id,
                'name': device.device_name,
                'deviceId': device.display_device_id,
                'approachDistance': 5.0,  # Default value
                'approachSeconds': device.signal_period or 30,
                'status': 'active'
            }
            device_settings.append(device_data)

        return Response({
            'success': True,
            'data': {
                'deviceSettings': device_settings
            }
        })

    def update(self, request, pk=None):
        """Update device settings"""
        try:
            device = DeviceMaster.objects.get(pk=pk)

            # Only update the signal_period field
            if 'approachSeconds' in request.data:
                device.signal_period = request.data['approachSeconds']
                device.save()

            # Return formatted response
            return Response({
                'success': True,
                'message': 'デバイス設定が更新されました',
                'data': {
                    'id': device.id,
                    'name': device.device_name,
                    'deviceId': device.display_device_id,
                    'approachDistance': 5.0,  # Default value
                    'approachSeconds': device.signal_period or 30,
                    'status': 'active'
                }
            })

        except DeviceMaster.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Device not found'
            }, status=status.HTTP_404_NOT_FOUND)

# Work Views
class WorkViewSet(viewsets.ViewSet):
    """ViewSet for WorkInfo model"""
    permission_classes = [IsAdminOrManager]  # Both admin and manager can access work information

    def list(self, request):
        """Get all works"""
        works = WorkInfo.objects.all()
        work_names = list(set([work.work_name for work in works if work.work_name]))

        return Response({
            'success': True,
            'data': {
                'works': work_names
            }
        })

    def retrieve(self, request, pk=None):
        """Get work details by work name"""
        try:
            # Get all work groups with this work name
            work_groups = WorkInfo.objects.filter(work_name=pk)

            if not work_groups.exists():
                raise WorkInfo.DoesNotExist

            serializer = WorkGroupSerializer(work_groups, many=True)

            return Response({
                'success': True,
                'data': {
                    'workGroups': serializer.data
                }
            })
        except WorkInfo.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Work not found'
            }, status=status.HTTP_404_NOT_FOUND)

# Alert Views
class AlertViewSet(viewsets.ViewSet):
    """ViewSet for HistoricalJudgeAlertInstruction model"""
    permission_classes = [IsAdmin]  # Only admin can manage alerts

    def list(self, request):
        """Get all alerts"""
        alerts = HistoricalJudgeAlertInstruction.objects.all().order_by('-created_at')
        serializer = AlertSerializer(alerts, many=True)

        return Response({
            'success': True,
            'data': {
                'alerts': serializer.data
            }
        })

    def retrieve(self, request, pk=None):
        """Get alert by ID"""
        try:
            alert = HistoricalJudgeAlertInstruction.objects.get(pk=pk)
            serializer = AlertSerializer(alert)
            return Response({
                'success': True,
                'data': serializer.data
            })
        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'], url_path='mark-read')
    def mark_as_read(self, request, pk=None):
        """Mark alert as read"""
        try:
            # Since there's no is_read field in the LIPS model, we just acknowledge the request
            alert = HistoricalJudgeAlertInstruction.objects.get(pk=pk)

            return Response({
                'success': True,
                'message': 'アラートが既読としてマークされました'
            })
        except HistoricalJudgeAlertInstruction.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Alert not found'
            }, status=status.HTTP_404_NOT_FOUND)