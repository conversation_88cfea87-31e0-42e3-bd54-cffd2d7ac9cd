# Notification Alert API Documentation

This document provides comprehensive documentation for the Notification Alert API, which is designed to manage devices, alerts, and work operations.

## Base URL

```
http://localhost:8000/api/v1/
```

## Authentication

All API endpoints require JWT authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

To obtain a JWT token, use the authentication endpoints provided by the User Management API.

## Authentication Endpoints

Authentication endpoints are provided by the User Management API at `/userapi/`:

- **Login**: `POST /userapi/login/`
- **Register**: `POST /userapi/register/`
- **Forgot Password**: `POST /userapi/forgot-password/`
- **Reset Password**: `POST /userapi/reset-password/`

For detailed documentation on these endpoints, please refer to the User Management API documentation.

## Response Format

All API responses follow a standard format:

```json
{
  "success": true|false,
  "message": "Optional message",
  "data": {
    // Response data
  }
}
```

## User Endpoints

### Get Current User

Retrieves the profile of the currently authenticated user.

- **URL**: `/users/me/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "id": "1",
      "name": "山田 太郎",
      "email": "<EMAIL>",
      "role": "アドミン",
      "date_joined": "2023-10-15T00:00:00Z",
      "last_login": "2025-04-22T10:30:15Z"
    }
  }
  ```

### Update User Profile

Updates the profile of the currently authenticated user.

- **URL**: `/users/me/`
- **Method**: `PUT`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "name": "山田 太郎",
    "email": "<EMAIL>"
  }
  ```
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "message": "プロフィールが更新されました",
    "data": {
      "id": "1",
      "name": "山田 太郎",
      "email": "<EMAIL>",
      "role": "アドミン",
      "date_joined": "2023-10-15T00:00:00Z",
      "last_login": "2025-04-22T10:30:15Z"
    }
  }
  ```

## Device Endpoints

### Get All Devices

Retrieves a list of all devices.

- **URL**: `/devices/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "devices": [
        {
          "id": "1",
          "name": "motomachi",
          "device_id": "0010",
          "assigned_work": "SVLR0008",
          "charge": 59,
          "status": "normal",
          "usage_time": null,
          "settings": {
            "id": 1,
            "approach_distance": 5.0,
            "approach_seconds": 30,
            "status": "active"
          }
        },
        {
          "id": "2",
          "name": "Device 2",
          "device_id": "0020",
          "assigned_work": "SVLR0010",
          "charge": 75,
          "status": "normal",
          "usage_time": "01:23:45",
          "settings": {
            "id": 2,
            "approach_distance": 7.5,
            "approach_seconds": 45,
            "status": "active"
          }
        }
      ]
    }
  }
  ```

### Get Device by ID

Retrieves a specific device by ID.

- **URL**: `/devices/{id}/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "id": "1",
      "name": "motomachi",
      "device_id": "0010",
      "assigned_work": "SVLR0008",
      "charge": 59,
      "status": "normal",
      "usage_time": null,
      "settings": {
        "id": 1,
        "approach_distance": 5.0,
        "approach_seconds": 30,
        "status": "active"
      }
    }
  }
  ```
- **Error Response (404 Not Found)**:
  ```json
  {
    "success": false,
    "message": "Device not found"
  }
  ```

### Update Device Assignment

Assigns or removes a work assignment for a device.

- **URL**: `/devices/{id}/assign/`
- **Method**: `PUT`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "assigned_work": "SVLR0008"
  }
  ```
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "message": "デバイスの割り当てが更新されました",
    "data": {
      "id": "1",
      "name": "motomachi",
      "device_id": "0010",
      "assigned_work": "SVLR0008",
      "charge": 59,
      "status": "normal",
      "usage_time": null,
      "settings": {
        "id": 1,
        "approach_distance": 5.0,
        "approach_seconds": 30,
        "status": "active"
      }
    }
  }
  ```
- **Error Response (400 Bad Request)**:
  ```json
  {
    "success": false,
    "message": "Work 'SVLR0008' does not exist"
  }
  ```

### Get Device Settings

Retrieves settings for all devices.

- **URL**: `/device-settings/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "deviceSettings": [
        {
          "id": "1",
          "name": "motomachi",
          "deviceId": "0010",
          "approachDistance": 5.0,
          "approachSeconds": 30,
          "status": "active"
        },
        {
          "id": "2",
          "name": "Device 2",
          "deviceId": "0020",
          "approachDistance": 7.5,
          "approachSeconds": 45,
          "status": "active"
        }
      ]
    }
  }
  ```

### Update Device Settings

Updates settings for a specific device.

- **URL**: `/device-settings/{id}/`
- **Method**: `PUT`
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "approach_distance": 5.5,
    "approach_seconds": 35
  }
  ```
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "message": "デバイス設定が更新されました",
    "data": {
      "id": "1",
      "name": "motomachi",
      "deviceId": "0010",
      "approachDistance": 5.5,
      "approachSeconds": 35,
      "status": "active"
    }
  }
  ```
- **Error Response (404 Not Found)**:
  ```json
  {
    "success": false,
    "message": "Device not found"
  }
  ```

## Work/Operation Endpoints

### Get All Works

Retrieves a list of all works/operations.

- **URL**: `/works/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "works": [
        "SVLR0008",
        "SVLR0010",
        "SVLR0012"
      ]
    }
  }
  ```

### Get Work Details

Retrieves detailed information for a specific work.

- **URL**: `/works/{id}/details/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "workGroups": [
        {
          "id": "1",
          "work": "SVLR0008",
          "group_number": "1111",
          "time_elapsed": "07:48:59.037",
          "assigned_devices": 0,
          "accessible_areas": 3,
          "createdAt": "2025-05-27 06:22:22"
        },
        {
          "id": "2",
          "work": "SVLR0008",
          "group_number": "1112",
          "time_elapsed": "05:30:20.124",
          "assigned_devices": 2,
          "accessible_areas": 5,
          "createdAt": "2025-05-27 06:22:22"
        }
      ]
    }
  }
  ```
- **Error Response (404 Not Found)**:
  ```json
  {
    "success": false,
    "message": "Work not found"
  }
  ```

## Alert Endpoints

### Get All Alerts

Retrieves a list of all alerts.

- **URL**: `/alerts/`
- **Method**: `GET`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "data": {
      "alerts": [
        {
          "id": "1",
          "device": {
            "id": "1",
            "name": "motomachi",
            "device_id": "0010",
            "assigned_work": "SVLR0008",
            "charge": 59,
            "status": "normal",
            "usage_time": null,
            "settings": {
              "id": 1,
              "approach_distance": 5.0,
              "approach_seconds": 30,
              "status": "active"
            }
          },
          "alert_type": "approach",
          "message": "Device is approaching a prohibited area",
          "is_read": false,
          "created_at": "2025-05-01T09:30:15Z"
        }
      ]
    }
  }
  ```

### Mark Alert as Read

Marks a specific alert as read.

- **URL**: `/alerts/{id}/mark-read/`
- **Method**: `POST`
- **Authentication**: Required
- **Success Response (200 OK)**:
  ```json
  {
    "success": true,
    "message": "アラートが既読としてマークされました"
  }
  ```
- **Error Response (404 Not Found)**:
  ```json
  {
    "success": false,
    "message": "Alert not found"
  }
  ```

## Data Models

### Device

```
{
  "id": "1",                      // Unique identifier
  "name": "motomachi",            // Device name
  "device_id": "0010",            // Device ID (unique)
  "assigned_work": "SVLR0008",    // Assigned work (can be null)
  "charge": 59,                   // Battery percentage
  "status": "normal",             // Status (normal, warning, error, inactive)
  "usage_time": "01:23:45",       // Usage time (can be null)
  "settings": {                   // Device settings
    "id": 1,
    "approach_distance": 5.0,     // Approach distance in meters
    "approach_seconds": 30,       // Approach seconds
    "status": "active"            // Status (active, inactive)
  }
}
```

### Work

```
{
  "id": "1",                      // Unique identifier
  "work_name": "SVLR0008",        // Work name (unique)
  "groups": [                     // Work groups
    {
      "id": "1",
      "work": "SVLR0008",
      "group_number": "1111",     // Group number
      "time_elapsed": "07:48:59", // Time elapsed
      "assigned_devices": 0,      // Number of assigned devices
      "accessible_areas": 3       // Number of accessible areas
    }
  ]
}
```

### Alert

```
{
  "id": "1",                      // Unique identifier
  "device": {                     // Device that triggered the alert
    // Device object
  },
  "alert_type": "approach",       // Alert type (approach, battery, error, offline)
  "message": "...",               // Alert message
  "is_read": false,               // Whether the alert has been read
  "created_at": "2025-05-01T09:30:15Z" // Creation timestamp
}
```

### User

```
{
  "id": "1",                      // Unique identifier
  "name": "山田 太郎",             // User name
  "email": "<EMAIL>", // Email (unique)
  "role": "アドミン",              // Role (admin, manager, user)
  "date_joined": "2023-10-15T00:00:00Z", // Join date
  "last_login": "2025-04-22T10:30:15Z"   // Last login timestamp
}
```

## Error Codes

- **400 Bad Request**: Invalid request parameters or validation error
- **401 Unauthorized**: Authentication required or invalid credentials
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error
