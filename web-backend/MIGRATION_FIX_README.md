# Migration Fix Instructions

This document provides instructions on how to fix the database migration issue where the error `Table 'lipsweb.lips_device_receive_data' doesn't exist` occurs.

## Problem Description

The issue is caused by migration `0005_auto_20250511_1809.py` which drops and recreates the entire database. This is problematic because:

1. It removes all tables from the database
2. <PERSON><PERSON><PERSON> still thinks the tables exist
3. When trying to apply subsequent migrations, <PERSON><PERSON><PERSON> fails because it can't find the tables

Additionally, there was a conflict in the migration graph with multiple leaf nodes.

## Solution

We've created a fix with the following changes:

1. Disabled the problematic migration that was dropping the database
2. Created a merge migration to resolve the conflicting migration paths
3. Created a new migration `0012_fix_database_structure.py` that properly creates all necessary tables
4. Created a script to help reset and reapply migrations

## How to Fix

### Option 1: Reset and Reapply Migrations (Recommended for Development)

This option is best if you don't have important data in your database or if you're in development.

1. Make sure your database exists:
   ```sql
   CREATE DATABASE IF NOT EXISTS lipsweb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Run the reset script:
   ```bash
   python reset_migrations.py
   ```

   This will:
   - Fake revert all migrations for the lips app
   - Apply migrations up to the merge migration
   - Apply the fix migration
   - Apply remaining migrations

3. If you encounter issues, you can try using the `--fake` flag:
   ```bash
   python reset_migrations.py --fake
   ```

4. If you want to skip the initial revert step:
   ```bash
   python reset_migrations.py --skip-zero
   ```

### Option 2: Manual Fix (For Production)

If you're in production and can't reset migrations, follow these steps:

1. Make sure your database exists:
   ```sql
   CREATE DATABASE IF NOT EXISTS lipsweb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Apply migrations up to the merge migration:
   ```bash
   python manage.py migrate lips 0011_merge_20250517_0553
   ```

3. Apply the fix migration:
   ```bash
   python manage.py migrate lips 0012_fix_database_structure
   ```

4. Continue with the remaining migrations:
   ```bash
   python manage.py migrate lips
   ```

5. If you encounter issues, you might need to use the `--fake` flag:
   ```bash
   python manage.py migrate lips --fake
   ```

## Preventing Future Issues

To prevent similar issues in the future:

1. Never use `DROP DATABASE` in migrations - it's too destructive
2. Use `CREATE TABLE IF NOT EXISTS` instead of dropping and recreating tables
3. Consider using Django's built-in migration operations instead of raw SQL when possible
4. Always back up your database before applying migrations
5. Regularly run `python manage.py makemigrations --check` to detect migration conflicts early

## Additional Notes

- The MySQL database configuration in your settings.py uses port 3307, make sure this is correct for your environment
- If you're still having issues, you might need to manually create the tables in your database
