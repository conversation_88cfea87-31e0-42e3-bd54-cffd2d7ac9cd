#!/bin/bash

echo "==================================="
echo "LIPS Web Application Starter"
echo "==================================="
echo

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Check if Python is installed
if ! command_exists python3; then
    echo "Python 3 is not installed. Please install Python 3.8 or higher."
    read -p "Press Enter to exit..."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "Python 3.8 or higher is required. You have Python $PYTHON_VERSION"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✓ Python $PYTHON_VERSION detected"

# Check if pip is installed
if ! command_exists pip3; then
    echo "pip3 is not installed. Please install pip for Python 3."
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✓ pip is installed"

# Check if MySQL is installed
if command_exists mysql; then
    echo "✓ MySQL is installed"
else
    echo "⚠ MySQL might not be installed"
    echo "You'll need to install MySQL and create a database named 'lipsweb'"
    read -p "Do you want to continue anyway? (y/n): " continue_without_mysql
    if [ "$continue_without_mysql" != "y" ]; then
        exit 1
    fi
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "Failed to create virtual environment."
        read -p "Press Enter to exit..."
        exit 1
    fi
    echo "✓ Virtual environment created"
else
    echo "✓ Virtual environment already exists"
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo "Failed to activate virtual environment."
    read -p "Press Enter to exit..."
    exit 1
fi
echo "✓ Virtual environment activated"

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install dependencies from requirements.txt."
    read -p "Press Enter to exit..."
    exit 1
fi

# Explicitly install PyMySQL
echo "Installing PyMySQL..."
pip install PyMySQL
if [ $? -ne 0 ]; then
    echo "Failed to install PyMySQL."
    read -p "Press Enter to exit..."
    exit 1
fi
echo "✓ Dependencies installed"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file..."
    # Generate a random secret key
    SECRET_KEY=$(python3 -c 'import random; import string; print("".join(random.choice(string.ascii_letters + string.digits + string.punctuation) for _ in range(50)))')

    # Get database configuration
    echo "Please provide your MySQL database configuration:"
    read -p "Database name (default: lipsweb): " DB_NAME
    DB_NAME=${DB_NAME:-lipsweb}

    read -p "Database user (default: root): " DB_USER
    DB_USER=${DB_USER:-root}

    read -p "Database password: " DB_PASSWORD

    read -p "Database host (default: localhost): " DB_HOST
    DB_HOST=${DB_HOST:-localhost}

    read -p "Database port (default: 3306): " DB_PORT
    DB_PORT=${DB_PORT:-3306}

    # Create .env file
    cat > .env << EOF
DJANGO_SECRET_KEY=$SECRET_KEY
DEBUG=True
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
EOF
    echo "✓ .env file created"
else
    echo "✓ .env file already exists"
fi

# Create database if it doesn't exist
echo "Checking if database exists..."
DB_NAME=$(grep DB_NAME .env | cut -d= -f2)
DB_USER=$(grep DB_USER .env | cut -d= -f2)
DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d= -f2)
DB_HOST=$(grep DB_HOST .env | cut -d= -f2)
DB_PORT=$(grep DB_PORT .env | cut -d= -f2)

if command_exists mysql; then
    echo "Attempting to create database if it doesn't exist..."

    # Create database creation SQL
    cat > create_db.sql << EOF
CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EOF

    # Try to create the database
    if [ -z "$DB_PASSWORD" ]; then
        # No password
        mysql -u $DB_USER -h $DB_HOST -P $DB_PORT < create_db.sql
    else
        # With password
        mysql -u $DB_USER -p"$DB_PASSWORD" -h $DB_HOST -P $DB_PORT < create_db.sql
    fi

    if [ $? -eq 0 ]; then
        echo "✓ Database checked/created successfully"
        rm create_db.sql
    else
        echo "Failed to create database. You may need to create it manually:"
        echo "mysql -u $DB_USER -p"
        echo "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        read -p "Do you want to continue anyway? (y/n): " continue_without_db
        if [ "$continue_without_db" != "y" ]; then
            deactivate
            rm create_db.sql 2>/dev/null
            exit 1
        fi
        rm create_db.sql 2>/dev/null
    fi
else
    echo "MySQL command not found. Cannot create database automatically."
    echo "You may need to create the database manually before proceeding."
fi

# Run migrations
echo "Running database migrations..."
python manage.py migrate
if [ $? -ne 0 ]; then
    echo "Failed to run migrations."
    echo "This might be due to database connection issues."
    read -p "Do you want to continue anyway? (y/n): " continue_without_migrations
    if [ "$continue_without_migrations" != "y" ]; then
        deactivate
        exit 1
    fi
else
    echo "✓ Migrations completed"
fi

# Populate database with initial data
echo "Populating database with initial data..."
python populate_data.py
if [ $? -ne 0 ]; then
    echo "Failed to populate database with initial data."
    read -p "Do you want to continue anyway? (y/n): " continue_without_data
    if [ "$continue_without_data" != "y" ]; then
        deactivate
        exit 1
    fi
else
    echo "✓ Database populated successfully"
fi

# Generate token
echo "Generating access token..."
python generate_token.py
if [ $? -ne 0 ]; then
    echo "Failed to generate token, but this is not critical."
fi

# Get port number if provided
PORT=${1:-8000}

# Start server
echo ""
echo "=== Starting Django Server ==="
echo "✓ Setup completed successfully!"
echo "Starting the Django development server on port $PORT..."
echo "Press Ctrl+C to stop the server"
echo ""

python manage.py runserver 0.0.0.0:$PORT

# Deactivate virtual environment when server stops
deactivate

echo "Server stopped"
read -p "Press Enter to exit..."
