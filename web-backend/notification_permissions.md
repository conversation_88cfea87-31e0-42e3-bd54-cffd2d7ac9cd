# Notification API Permission Structure

This document outlines the permission structure for the Notification API, which restricts access to specific roles.

## Role-Based Permissions

The API uses role-based permissions to control access to different endpoints. There are three main roles:

1. **Admin**: Has access to user management and system-wide alerts
2. **Manager**: Has access to device management and settings
3. **User**: Has limited access (not implemented in the current API)

## Permission Classes

Three permission classes have been implemented:

1. **IsAdmin**: Allows access only to users with the 'admin' role
2. **IsManager**: Allows access only to users with the 'manager' role
3. **IsAdminOrManager**: Allows access to users with either 'admin' or 'manager' roles

## API Endpoints and Required Roles

| Endpoint | HTTP Method | Required Role | Description |
|----------|-------------|---------------|-------------|
| `/users/me/` | GET, PUT | Admin | View and update user profile |
| `/devices/` | GET, POST | Manager | List and create devices |
| `/devices/:id/` | GET, PUT, DELETE | Manager | View, update, and delete devices |
| `/devices/:id/assign/` | PUT | Manager | Assign work to devices |
| `/devices/settings/` | GET | Manager | List device settings |
| `/devices/settings/:id/` | PUT | Manager | Update device settings |
| `/works/` | GET | Admin or Manager | List all works |
| `/works/:workName/` | GET | Admin or Manager | Get work details |
| `/alerts/` | GET, POST | Admin | List and create alerts |
| `/alerts/:id/` | GET, PUT, DELETE | Admin | View, update, and delete alerts |
| `/alerts/:id/mark-read/` | POST | Admin | Mark alert as read |

## Implementation Details

The permission classes check the user's role by examining the `role` field on the user model. If the user doesn't have a role or the role doesn't match the required role, access is denied.

## Testing Permissions

To test these permissions:

1. Create users with different roles:
   ```python
   from usermanagement.models import Role, User
   admin_role = Role.objects.get(name='admin')
   manager_role = Role.objects.get(name='manager')
   
   # Create admin user
   admin_user = User.objects.create_user(username='admin', email='<EMAIL>', password='password')
   admin_user.role = admin_role
   admin_user.save()
   
   # Create manager user
   manager_user = User.objects.create_user(username='manager', email='<EMAIL>', password='password')
   manager_user.role = manager_role
   manager_user.save()
   ```

2. Obtain authentication tokens for each user
3. Test API endpoints with different user tokens to verify permission restrictions
