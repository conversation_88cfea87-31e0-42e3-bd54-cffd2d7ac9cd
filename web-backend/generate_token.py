import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from rest_framework_simplejwt.tokens import RefreshToken
from usermanagement.models import User

def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }

# Get the admin user
# Import required modules
from django.contrib.auth.hashers import make_password
from usermanagement.models import Role
from django.db import IntegrityError

def generate_token_for_user(user):
    """Generate and print token for a user"""
    tokens = get_tokens_for_user(user)

    print(f"\n=== JWT Tokens for User: {user.username} ===")
    print("Access Token:")
    print(tokens['access'])
    print("\nRefresh Token:")
    print(tokens['refresh'])
    print("\nUse the access token in your Authorization header as:")
    print("Bearer", tokens['access'])
    print("===========================================\n")

    return tokens

# Try to find an admin user
user = None

# First try to get the admin user
try:
    user = User.objects.get(username='admin')
    print("Found admin user.")
    generate_token_for_user(user)
except User.DoesNotExist:
    print("Admin user not found. Looking for test admin...")

    # Try to find the test admin user
    try:
        user = User.objects.get(username='testadmin')
        print("Found test admin user.")
        generate_token_for_user(user)
    except User.DoesNotExist:
        print("Test admin user not found. Creating a new test user...")

        # Get or create admin role
        admin_role, _ = Role.objects.get_or_create(name='admin')

        # Try to create a test user with a unique username
        test_username = 'testadmin'
        attempt = 0

        while user is None and attempt < 5:  # Limit attempts to avoid infinite loop
            try:
                user = User.objects.create(
                    username=test_username,
                    email=f'{test_username}@example.com',
                    password=make_password('testpassword'),
                    role=admin_role,
                    full_name='Test Admin',
                    status=True
                )

                print(f"Test user created with:")
                print(f"Username: {test_username}")
                print(f"Password: testpassword")

                generate_token_for_user(user)
            except IntegrityError:
                # Username already exists, try a different one
                attempt += 1
                test_username = f'testadmin{attempt}'
                print(f"Username 'testadmin' already exists. Trying '{test_username}'...")

        if user is None:
            print("Failed to create a test user after multiple attempts.")

            # As a last resort, try to find any user
            any_user = User.objects.first()
            if any_user:
                print(f"Using existing user '{any_user.username}' instead.")
                generate_token_for_user(any_user)
            else:
                print("No users found in the database. Please run the populate_data.py script first.")
