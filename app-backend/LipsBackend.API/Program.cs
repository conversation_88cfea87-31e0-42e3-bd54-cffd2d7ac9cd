using FluentValidation.AspNetCore;
using LipsBackend.API.Extensions;
using LipsBackend.API.Filters;
using LipsBackend.API.Middleware;
using LipsBackend.Service.Mappings;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers(options => 
{
    // Register the ApiResponse Filter
    options.Filters.Add<ApiResponseFilter>();
})
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.WriteIndented = true;
    });

builder.Services.AddEndpointsApiExplorer();

ProgramExtensions.ConfigureSwagger(builder); 
ProgramExtensions.ConfigureDatabase(builder);
ProgramExtensions.ConfigureJwtAuthentication(builder);

// Register AutoMapper with specific namespace to avoid ambiguity
builder.Services.AddAutoMapper(typeof(Program).Assembly);

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(MappingProfile));

ProgramExtensions.RegisterRepositories(builder);
ProgramExtensions.RegisterServices(builder);

// Register FluentValidation
builder.Services.AddFluentValidationAutoValidation();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Lips API v1");
        // Set Swagger UI as the start page
        c.RoutePrefix = string.Empty;
    });
}
else
{
    app.UseHsts();
}

// Use custom exception middleware
app.UseMiddleware<ExceptionHandlingMiddleware>();
app.UseHttpsRedirection();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.Run();