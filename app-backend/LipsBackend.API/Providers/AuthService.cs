using AutoMapper;
using LipsBackend.Domain.Helpers;
using LipsBackend.Repository.Interfaces;
using LipsBackend.Service.DTOs.Auth;

namespace LipsBackend.API.Providers;

public interface IAuthService
{
    Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest);
}


public class AuthService(IUserRepository userRepository, IMapper mapper, IConfiguration configuration)
    : IAuthService
{
    private readonly IMapper _mapper = mapper;

    public async Task<LoginResponseDto> LoginAsync(LoginRequestDto loginRequest)
    {
        var user = await userRepository.GetByEmailAsync(loginRequest.Email);
        if (user == null)
        {
            throw new Exception("Invalid email or password");
        }

        var isPasswordValid = AppHelper.VerifyDjangoPassword(loginRequest.Password, user.Password);
        if (!isPasswordValid)
        {
            throw new Exception("Invalid email or password");
        }

        await userRepository.UpdateAsync(user);

        // Generate JWT token
        var token = JwtProvider.GenerateJwtToken(configuration, user.Id.ToString(), user.Email);
        var refreshToken = JwtProvider.GenerateRefreshToken();

        return new LoginResponseDto
        {
            Token = token,
            RefreshToken = refreshToken,
            Email = user.Email,
            FullName = user.FullName
        };
    }
}