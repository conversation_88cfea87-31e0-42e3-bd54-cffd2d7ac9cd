using LipsBackend.API.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Net;

namespace LipsBackend.API.Filters;

/// <summary>
/// Action filter that wraps all API responses in a standardized ApiResponse object
/// </summary>
public class ApiResponseFilter : IActionFilter
{
    public void OnActionExecuting(ActionExecutingContext context)
    {
        // Nothing to do before the action executes
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // Don't wrap responses that are already ApiResponse objects
        if (context.Result is ObjectResult objectResult &&
            objectResult.Value is not ApiResponse<object>)
        {
            // Handle successful responses (including custom status codes)
            var statusCode = objectResult.StatusCode ?? (int)HttpStatusCode.OK;
            var isSuccess = statusCode >= 200 && statusCode < 400;

            if (isSuccess)
            {
                // Wrap successful response in ApiResponse
                objectResult.Value = ApiResponse<object>.Success(objectResult.Value);
            }
            else
            {
                // Handle error responses
                if (objectResult.Value is ProblemDetails problemDetails)
                {
                    objectResult.Value = ApiResponse<object>.Failure(problemDetails.Detail ?? problemDetails.Title ?? "An error occurred");
                }
                // Check for objects with a "message" property (like those returned by controllers)
                else if (objectResult.Value != null && 
                         objectResult.Value.GetType().GetProperty("message")?.GetValue(objectResult.Value) is string errorMessage)
                {
                    objectResult.Value = ApiResponse<object>.Failure(errorMessage);
                }
                // Dictionary with message key
                else if (objectResult.Value is IDictionary<string, object> errorDict && 
                         errorDict.TryGetValue("message", out var messageObj) && 
                         messageObj is string dictMessage)
                {
                    objectResult.Value = ApiResponse<object>.Failure(dictMessage);
                }
                else
                {
                    objectResult.Value = ApiResponse<object>.Failure("An error occurred. Please try again later.");
                }
            }
        }
        else if (context.Result is StatusCodeResult statusCodeResult)
        {
            // Handle status code results without content (like 204 No Content)
            var isSuccess = statusCodeResult.StatusCode >= 200 && statusCodeResult.StatusCode < 400;
            var response = isSuccess
                ? ApiResponse<object>.Success(null)
                : ApiResponse<object>.Failure("An error occurred. Please try again later.");

            context.Result = new ObjectResult(response)
            {
                StatusCode = statusCodeResult.StatusCode
            };
        }
    }
}