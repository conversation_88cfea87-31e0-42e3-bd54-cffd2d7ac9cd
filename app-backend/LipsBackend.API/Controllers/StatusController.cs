using Microsoft.AspNetCore.Mvc;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class StatusController(ILogger<StatusController> logger) : ControllerBase
{
    [HttpGet]
    public IActionResult Get()
    {
        return Ok(new { Status = "Healthy", Version = "1.0.0", Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") });
    }

    [HttpGet("error")]
    public IActionResult GetError()
    {
        logger.LogInformation("Testing error response");
        return BadRequest("This is a test error response");
    }
}