using LipsBackend.Service.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/v1/lips-work-info")]
public class WorkInfoController(
    ICurrentUserWorkInfoService currentUserWorkInfoService)
    : ControllerBase
{
    [HttpGet("work")]
    [Authorize]
    public async Task<IActionResult> GetCurrentUserWorkInfo()
    {
        try
        {
            var result = await currentUserWorkInfoService.GetCurrentUserWorkInfoAsync();
            
            if (result == null)
            {
                return NotFound("No work information found for current user");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error retrieving work information: {ex.Message}");
        }
    }
} 