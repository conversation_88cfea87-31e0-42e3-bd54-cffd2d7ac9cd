using LipsBackend.Service.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/v1/business-operator-masters")]
public class BusinessOperatorMasterController(
    IBusinessOperatorDeviceService businessOperatorDeviceService)
    : ControllerBase
{
    [HttpGet("device")]
    [Authorize]
    public async Task<IActionResult> GetBusinessOperatorDevices()
    {
        try
        {
            var result = await businessOperatorDeviceService.GetBusinessOperatorDevicesAsync();
            
            if (!result.Any())
            {
                return NotFound("No business operator devices found");
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest($"Error retrieving business operator devices: {ex.Message}");
        }
    }
} 