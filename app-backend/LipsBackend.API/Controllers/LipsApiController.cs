using LipsBackend.Service.DTOs;
using LipsBackend.Service.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/v1/lipsapi")]
public class LipsApiController(IWorkApprovalService workApprovalService) : ControllerBase
{
    [HttpPost("works/approve")]
    [Authorize]
    public async Task<IActionResult> ApproveWork([FromBody] WorkApprovalRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var result = await workApprovalService.ApproveWorkAsync(request);
            
            if (result != null)
                return Ok(new 
                { 
                    success = true,
                    message = result.Message,
                    data = new 
                    {
                        deviceId = result.DeviceId,
                        approvalStatus = result.ApprovalStatus,
                        updatedAt = result.UpdatedAt
                    }
                });
            
            return NotFound(new { success = false, message = "Device not found" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"An error occurred: {ex.Message}" });
        }
    }

    [HttpPost("works/reject")]
    [Authorize]
    public async Task<IActionResult> RejectWork([FromBody] WorkApprovalRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var result = await workApprovalService.RejectWorkAsync(request);
            
            if (result != null)
                return Ok(new 
                { 
                    success = true,
                    message = result.Message,
                    data = new 
                    {
                        deviceId = result.DeviceId,
                        approvalStatus = result.ApprovalStatus,
                        updatedAt = result.UpdatedAt
                    }
                });
            
            return NotFound(new { success = false, message = "Device not found" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"An error occurred: {ex.Message}" });
        }
    }
} 