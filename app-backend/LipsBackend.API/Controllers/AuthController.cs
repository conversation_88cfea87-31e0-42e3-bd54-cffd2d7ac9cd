using LipsBackend.API.Providers;
using LipsBackend.Service.DTOs.Auth;
using LipsBackend.Service.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/auth")]
public class AuthController(IAuthService authService, IUserService userService) : ControllerBase
{
    [HttpPost("login")]
    public async Task<IActionResult> Login([FromBody] LoginRequestDto loginRequest)
    {
        try
        {
            var result = await authService.LoginAsync(loginRequest);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("change-password")]
    [Authorize]
    public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequestDto changePasswordRequest)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        // Get current user ID from JWT token
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim == null || !long.TryParse(userIdClaim.Value, out var userId))
            return Unauthorized(new { message = "Invalid user token" });

        var result = await userService.ChangePasswordAsync(userId, changePasswordRequest);
        
        if (result)
            return Ok(new { message = "Password changed successfully" });
        
        return BadRequest(new { message = "Failed to change password. Please check your current password." });
    }
}