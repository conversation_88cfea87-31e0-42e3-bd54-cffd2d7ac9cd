using LipsBackend.Service.DTOs;
using LipsBackend.Service.DTOs.Auth;
using LipsBackend.Service.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LipsBackend.API.Controllers;

[ApiController]
[Route("api/v1/userapi")]
public class UserApiController(IUserService userService, IUserDeviceService userDeviceService) : ControllerBase
{
    [HttpPost("forgot-password")]
    public async Task<IActionResult> ForgotPassword([FromBody] ForgotPasswordRequestDto forgotPasswordRequest)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await userService.ForgotPasswordAsync(forgotPasswordRequest);
        
        if (result != null)
            return Ok(result);
        
        return BadRequest(new { message = "User not found with the provided email" });
    }

    [HttpPost("reset-password")]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequestDto resetPasswordRequest)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await userService.ResetPasswordAsync(resetPasswordRequest);
        
        if (result)
            return Ok(new { message = "Password reset successfully" });
        
        return BadRequest(new { message = "Failed to reset password" });
    }

    [HttpPost("users/add")]
    [Authorize]
    public async Task<IActionResult> AddUserWithDevice([FromBody] UserDeviceAssignmentRequestDto request)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var result = await userDeviceService.AssignDeviceToUserAsync(request);
            
            if (result != null)
                return Ok(new 
                { 
                    success = true,
                    message = "Device assigned to user successfully",
                    data = result
                });
            
            return BadRequest(new { success = false, message = "Failed to assign device to user" });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { success = false, message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { success = false, message = $"An error occurred: {ex.Message}" });
        }
    }
} 