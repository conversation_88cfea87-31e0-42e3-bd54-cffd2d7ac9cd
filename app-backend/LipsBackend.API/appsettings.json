{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=3306;Database=lips-web;User=root;Password=;"}, "Jwt": {"Key": "YourSuperSecretKey-ThisShouldBeStored-InASecureLocation", "Issuer": "LipsBackendAPI", "ExpiryInDays": "7"}, "LipsDatabase": {"Directory": "lips-database"}, "Email": {"SmtpHost": "smtp.gmail.com", "SmtpPort": 587, "UseTls": true, "FromEmail": "<EMAIL>", "FromName": "Lips APP", "Username": "<EMAIL>", "Password": "rtgx agyo njaq siez", "UseConsoleForDevelopment": false}}