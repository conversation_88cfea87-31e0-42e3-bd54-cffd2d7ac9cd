﻿using LipsBackend.API.Providers;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using LipsBackend.Repository.Repositories;
using LipsBackend.Service.Services;
using LipsBackend.Service.Interfaces;
using LipsBackend.Service.Configuration;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;

namespace LipsBackend.API.Extensions;

public class ProgramExtensions
{
    public static void ConfigureSwagger(WebApplicationBuilder builder)
    {
        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "Lips API", Version = "v1" });

            // Add JWT Authentication to Swagger
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                []
            }
        });
        });
    }

    public static void ConfigureDatabase(WebApplicationBuilder builder)
    {
        var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
        if (string.IsNullOrEmpty(connectionString))
        {
            throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
        }

        builder.Services.AddDbContext<AppDbContext>(options =>
        {
            options.UseMySql(
                connectionString,
                ServerVersion.AutoDetect(connectionString)
            );
        });
    }

    public static void ConfigureJwtAuthentication(WebApplicationBuilder builder)
    {
        var jwtKey = builder.Configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key is not configured");
        builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = builder.Configuration["Jwt:Issuer"],
                    ValidAudience = builder.Configuration["Jwt:Issuer"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtKey))
                };
            });
    }

    public static void RegisterRepositories(WebApplicationBuilder builder)
    {
        builder.Services.AddScoped<IUserRepository, UserRepository>();
        builder.Services.AddScoped<IDeviceRepository, DeviceRepository>();
        builder.Services.AddScoped<IWorkInfoRepository, WorkInfoRepository>();
        builder.Services.AddScoped<ILipsUserMasterRepository, LipsUserMasterRepository>();
        builder.Services.AddScoped<IBusinessOperatorMasterRepository, BusinessOperatorMasterRepository>();
    }

    public static void RegisterServices(WebApplicationBuilder builder)
    {
        // Add HttpContextAccessor for current user provider
        builder.Services.AddHttpContextAccessor();
        
        // Configure email settings
        builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("Email"));
        
        // Register providers
        builder.Services.AddScoped<ICurrentUserProvider, CurrentUserProvider>();
        
        // Register services
        builder.Services.AddScoped<IEmailService, EmailService>();
        builder.Services.AddScoped<IUserService, UserService>();
        builder.Services.AddScoped<ICurrentUserWorkInfoService, CurrentUserWorkInfoService>();
        builder.Services.AddScoped<IAuthService, AuthService>();
        builder.Services.AddScoped<IBusinessOperatorDeviceService, BusinessOperatorDeviceService>();
        builder.Services.AddScoped<IUserDeviceService, UserDeviceService>();
        builder.Services.AddScoped<IWorkApprovalService, WorkApprovalService>();
    }
}