using System.Text.Json.Serialization;

namespace LipsBackend.API.Models
{
    /// <summary>
    /// Standardized API response wrapper for all endpoints
    /// </summary>
    /// <typeparam name="T">Type of data being returned</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// Indicates if the API call was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// The main response data (null if operation failed)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public T? Data { get; set; }

        /// <summary>
        /// Error messages when operation fails (null if successful)
        /// </summary>
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        public List<string>? Errors { get; set; }

        /// <summary>
        /// Creates a successful response with data
        /// </summary>
        public static ApiResponse<T> Success(T data)
        {
            return new ApiResponse<T>
            {
                IsSuccess = true,
                Data = data
            };
        }

        /// <summary>
        /// Creates a failed response with error messages
        /// </summary>
        public static ApiResponse<T> Failure(string errorMessage)
        {
            return new ApiResponse<T>
            {
                IsSuccess = false,
                Errors = new List<string> { errorMessage }
            };
        }

        /// <summary>
        /// Creates a failed response with multiple error messages
        /// </summary>
        public static ApiResponse<T> Failure(List<string> errorMessages)
        {
            return new ApiResponse<T>
            {
                IsSuccess = false,
                Errors = errorMessages
            };
        }
    }
} 