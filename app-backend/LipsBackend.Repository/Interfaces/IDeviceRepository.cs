using LipsBackend.Domain.Entities;

namespace LipsBackend.Repository.Interfaces;

public interface IDeviceRepository
{
    Task<IEnumerable<Device>> GetByUserIdAsync(string userId);
    Task<IEnumerable<Device>> GetAllAsync();
    Task<Device?> GetByDisplayDeviceIdAsync(string displayDeviceId);
    Task<Device?> GetByIdAsync(long id);
    Task<Device> CreateAsync(Device device);
    Task<Device> UpdateAsync(Device device);
} 