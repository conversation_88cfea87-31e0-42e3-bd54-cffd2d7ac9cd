using LipsBackend.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Data;

public class AppDbContext : DbContext
{
    public AppDbContext()
    {
    }

    public AppDbContext(DbContextOptions<AppDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<User> Users { get; set; }
    public virtual DbSet<Device> Devices { get; set; }
    public virtual DbSet<WorkInfo> WorkInfos { get; set; }
    public virtual DbSet<UserMaster> LipsUserMasters { get; set; }
    public virtual DbSet<SettingsInfo> SettingsInfos { get; set; }
    public virtual DbSet<PermitedApproachInfo> PermittedApproachInfos { get; set; }
    public virtual DbSet<ProhibitedApproachInfo> ProhibitedApproachInfos { get; set; }
    public virtual DbSet<HistoricalJudgeAlertInstruction> HistoricalJudgeAlertInstructions { get; set; }
    public virtual DbSet<FixInfo> FixInfos { get; set; }
    public virtual DbSet<DeviceReceiveData> DeviceReceiveData { get; set; }
    public virtual DbSet<BusinessOperatorMaster> BusinessOperatorMasters { get; set; }
} 