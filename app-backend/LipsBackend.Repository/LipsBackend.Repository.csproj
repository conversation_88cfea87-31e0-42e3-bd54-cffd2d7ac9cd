﻿<Project Sdk="Microsoft.NET.Sdk">

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.13" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.13">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.13" />
		<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.4.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
		<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.3" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\LipsBackend.Domain\LipsBackend.Domain.csproj" />
	</ItemGroup>

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

</Project>