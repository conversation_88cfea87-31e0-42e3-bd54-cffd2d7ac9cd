using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Repositories;

public class WorkInfoRepository(AppDbContext context) : IWorkInfoRepository
{
    public async Task<IEnumerable<WorkInfo>> GetByUserIdAsync(string userId) =>
        await context.WorkInfos
            .Where(w => w.UserId == userId)
            .ToListAsync();
}