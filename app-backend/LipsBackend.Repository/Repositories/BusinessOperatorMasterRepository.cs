using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Repositories;

public class BusinessOperatorMasterRepository(AppDbContext context) : IBusinessOperatorMasterRepository
{
    public async Task<BusinessOperatorMaster?> GetByIdAsync(int id) =>
        await context.BusinessOperatorMasters
            .FirstOrDefaultAsync(b => b.Id == id);

    public async Task<IEnumerable<BusinessOperatorMaster>> GetAllAsync() =>
        await context.BusinessOperatorMasters.ToListAsync();
} 