using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Repositories;

public class LipsUserMasterRepository(AppDbContext context) : ILipsUserMasterRepository
{
    public async Task<UserMaster?> GetByUserIdAsync(long userId) =>
        await context.LipsUserMasters
            .FirstOrDefaultAsync(u => u.UserId == userId);

    public async Task<UserMaster> CreateAsync(UserMaster userMaster)
    {
        context.LipsUserMasters.Add(userMaster);
        await context.SaveChangesAsync();
        return userMaster;
    }
} 