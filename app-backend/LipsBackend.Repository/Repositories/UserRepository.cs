using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Repositories;

public class UserRepository(AppDbContext context) : IUserRepository
{
    public async Task<User?> GetByEmailAsync(string email) => await context.Users.FirstOrDefaultAsync(u => u.Email == email);

    public async Task<User?> GetByIdAsync(long id) => await context.Users.FirstOrDefaultAsync(u => u.Id == id);

    public async Task<User> UpdateAsync(User user)
    {
        context.Users.Update(user);
        await context.SaveChangesAsync();
        return user;
    }
}