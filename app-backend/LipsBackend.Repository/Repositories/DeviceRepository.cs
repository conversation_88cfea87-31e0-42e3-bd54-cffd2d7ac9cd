using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Data;
using LipsBackend.Repository.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace LipsBackend.Repository.Repositories;

public class DeviceRepository(AppDbContext context) : IDeviceRepository
{
    public async Task<IEnumerable<Device>> GetByUserIdAsync(string userId) =>
        await context.Devices
            .Where(d => d.UserId == userId)
            .ToListAsync();

    public async Task<IEnumerable<Device>> GetAllAsync() =>
        await context.Devices.ToListAsync();

    public async Task<Device?> GetByDisplayDeviceIdAsync(string displayDeviceId) =>
        await context.Devices
            .FirstOrDefaultAsync(d => d.DisplayDeviceId == displayDeviceId);

    public async Task<Device?> GetByIdAsync(long id) =>
        await context.Devices
            .FirstOrDefaultAsync(d => d.Id == id);

    public async Task<Device> CreateAsync(Device device)
    {
        context.Devices.Add(device);
        await context.SaveChangesAsync();
        return device;
    }

    public async Task<Device> UpdateAsync(Device device)
    {
        context.Devices.Update(device);
        await context.SaveChangesAsync();
        return device;
    }
} 