using AutoMapper;
using LipsBackend.Domain.Entities;
using LipsBackend.Service.DTOs;

namespace LipsBackend.Service.Mappings;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        // User mappings
        CreateMap<User, UserDto>();
        CreateMap<UserDto, User>();

        // Device mappings
        CreateMap<Device, DeviceDto>();
        CreateMap<DeviceDto, Device>();

        // WorkInfo mappings
        CreateMap<WorkInfo, WorkInfoDto>();
        CreateMap<WorkInfoDto, WorkInfo>();

        // User Device Assignment mappings
        CreateMap<Device, UserDeviceAssignmentResponseDto>()
            .ForMember(dest => dest.DeviceId, opt => opt.MapFrom(src => src.DisplayDeviceId))
            .ForMember(dest => dest.AssignedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        // Work Approval mappings
        CreateMap<Device, WorkApprovalResponseDto>()
            .ForMember(dest => dest.DeviceId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(dest => dest.Success, opt => opt.MapFrom(src => true));
    }
} 