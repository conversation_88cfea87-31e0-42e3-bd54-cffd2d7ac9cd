using LipsBackend.Repository.Data;
using LipsBackend.Service.DTOs;
using LipsBackend.Service.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Net;

namespace LipsBackend.Service.Services;

public class BusinessOperatorDeviceService(
    AppDbContext context, 
    ICurrentUserProvider currentUserProvider)
    : IBusinessOperatorDeviceService
{
    public async Task<IEnumerable<BusinessOperatorDeviceDto>> GetBusinessOperatorDevicesAsync()
    {
        // Step 1: Get current logged in user ID
        var currentUserId = currentUserProvider.GetCurrentUserId();
        if (currentUserId == null)
            return new List<BusinessOperatorDeviceDto>();

        // Step 2: Find user in lips_user_masters table to get internal user ID
        var userMaster = await context.LipsUserMasters
            .FirstOrDefaultAsync(u => u.UserId == currentUserId.Value);
        
        if (userMaster == null)
            return new List<BusinessOperatorDeviceDto>();

        // Step 3: Find devices for this user in lips_device_masters table
        var devices = await context.Devices
            .Where(d => d.UserId == userMaster.Id)
            .ToListAsync();

        if (!devices.Any())
            return new List<BusinessOperatorDeviceDto>();

        // Step 4 & 5: Get business operators for devices in a single query (avoiding N+1 problem)
        var businessOperatorIds = devices
            .Where(d => d.BusinessOperatorId.HasValue && !string.IsNullOrEmpty(d.DisplayDeviceId))
            .Select(d => d.BusinessOperatorId!.Value)
            .Distinct()
            .ToList();

        if (!businessOperatorIds.Any())
            return new List<BusinessOperatorDeviceDto>();

        var businessOperators = await context.BusinessOperatorMasters
            .Where(b => businessOperatorIds.Contains(b.Id))
            .ToListAsync();

        // Step 6: Create result by joining devices with business operators and resolve domain names to IPs
        var deviceBusinessOperatorPairs = (from device in devices
                                          join businessOperator in businessOperators
                                          on device.BusinessOperatorId equals businessOperator.Id
                                          where !string.IsNullOrEmpty(device.DisplayDeviceId)
                                          select new
                                          {
                                              DomainName = businessOperator.DomainName,
                                              DisplayDeviceId = device.DisplayDeviceId!
                                          }).ToList();

        var result = new List<BusinessOperatorDeviceDto>();

        foreach (var pair in deviceBusinessOperatorPairs)
        {
            var ipAddress = await ResolveDomainToIpAsync(pair.DomainName);
            result.Add(new BusinessOperatorDeviceDto
            {
                domain_name = ipAddress,
                display_device_id = pair.DisplayDeviceId
            });
        }

        return result;
    }

    /// <summary>
    /// Resolves a domain name to its IP address. If resolution fails or the input is already an IP, returns the original value.
    /// </summary>
    /// <param name="domainName">Domain name to resolve (e.g., "www.globalsolutions.com")</param>
    /// <returns>IP address string (e.g., "*************") or original value if resolution fails</returns>
    private async Task<string> ResolveDomainToIpAsync(string domainName)
    {
        try
        {
            // Check if the input is already an IP address
            if (IPAddress.TryParse(domainName, out _))
            {
                return domainName; // Already an IP address
            }

            // Remove protocol prefixes if present (http://, https://, etc.)
            var cleanDomainName = domainName;
            if (domainName.StartsWith("http://"))
                cleanDomainName = domainName.Substring(7);
            else if (domainName.StartsWith("https://"))
                cleanDomainName = domainName.Substring(8);

            // Remove port number if present
            var colonIndex = cleanDomainName.IndexOf(':');
            if (colonIndex > 0)
                cleanDomainName = cleanDomainName.Substring(0, colonIndex);

            // Remove path if present
            var slashIndex = cleanDomainName.IndexOf('/');
            if (slashIndex > 0)
                cleanDomainName = cleanDomainName.Substring(0, slashIndex);

            // Resolve the domain name to IP address
            var hostEntry = await Dns.GetHostEntryAsync(cleanDomainName);
            var ipAddress = hostEntry.AddressList.FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);
            
            return ipAddress?.ToString() ?? domainName; // Return IP or original if no IPv4 found
        }
        catch (Exception)
        {
            // If DNS resolution fails, return the original domain name
            // This ensures the API doesn't break if DNS resolution fails
            return domainName;
        }
    }
} 