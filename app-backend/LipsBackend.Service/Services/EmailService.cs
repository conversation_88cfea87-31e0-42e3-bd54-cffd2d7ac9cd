using LipsBackend.Service.Configuration;
using LipsBackend.Service.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Mail;

namespace LipsBackend.Service.Services;

public class EmailService : IEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<EmailService> _logger;

    public EmailService(IOptions<EmailSettings> emailSettings, ILogger<EmailService> logger)
    {
        _emailSettings = emailSettings.Value;
        _logger = logger;
    }

    public async Task<bool> SendOtpEmailAsync(string toEmail, string otp)
    {
        var subject = "Your Password Reset OTP";
        var message = $"Your OTP for password reset is: {otp}. It will expire in 10 minutes.";
        
        return await SendEmailAsync(toEmail, subject, message);
    }

    public async Task<bool> SendEmailAsync(string toEmail, string subject, string message)
    {
        try
        {
            // For development, log to console (similar to Django's console backend)
            if (_emailSettings.UseConsoleForDevelopment)
            {
                _logger.LogInformation("=== EMAIL CONSOLE OUTPUT (DEVELOPMENT MODE) ===");
                _logger.LogInformation($"From: {_emailSettings.FromEmail}");
                _logger.LogInformation($"To: {toEmail}");
                _logger.LogInformation($"Subject: {subject}");
                _logger.LogInformation($"Message: {message}");
                _logger.LogInformation("=============================================");
                return true; // Simulate successful sending in development
            }

            // For production, send actual email via SMTP
            _logger.LogInformation($"Sending email to {toEmail} via SMTP...");
            
            using var smtpClient = new SmtpClient(_emailSettings.SmtpHost, _emailSettings.SmtpPort);
            smtpClient.EnableSsl = _emailSettings.UseTls;
            
            // Validate SMTP credentials are configured for production
            if (string.IsNullOrEmpty(_emailSettings.Username) || string.IsNullOrEmpty(_emailSettings.Password))
            {
                _logger.LogError("SMTP credentials (Username/Password) are not configured in appsettings.json");
                throw new InvalidOperationException("SMTP credentials are required for production email sending");
            }
            
            smtpClient.Credentials = new NetworkCredential(_emailSettings.Username, _emailSettings.Password);

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName),
                Subject = subject,
                Body = message,
                IsBodyHtml = false
            };
            
            mailMessage.To.Add(toEmail);

            await smtpClient.SendMailAsync(mailMessage);
            _logger.LogInformation($"Email sent successfully to {toEmail}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Failed to send email to {toEmail}");
            return false;
        }
    }
} 