using LipsBackend.Domain.Entities;
using LipsBackend.Repository.Interfaces;
using LipsBackend.Service.DTOs;
using LipsBackend.Service.Interfaces;

namespace LipsBackend.Service.Services;

public class UserDeviceService(
    IDeviceRepository deviceRepository,
    ILipsUserMasterRepository userMasterRepository,
    IUserRepository userRepository,
    ICurrentUserProvider currentUserProvider) : IUserDeviceService
{
    public async Task<UserDeviceAssignmentResponseDto?> AssignDeviceToUserAsync(UserDeviceAssignmentRequestDto request)
    {
        // Get current logged-in user
        var currentUserId = currentUserProvider.GetCurrentUserId();
        if (currentUserId == null)
            return null;

        // Get the actual user to get the username
        var currentUser = await userRepository.GetByIdAsync(currentUserId.Value);
        if (currentUser == null)
            return null;

        // Find or create UserMaster record for the current user
        var userMaster = await userMasterRepository.GetByUserIdAsync(currentUserId.Value);
        
        string userMasterId;
        if (userMaster == null)
        {
            // Create new UserMaster record using username as ID (following existing pattern)
            userMasterId = currentUser.Username; // Use username as the UserMaster ID
            
            var newUserMaster = new UserMaster
            {
                Id = userMasterId,
                UserId = currentUserId.Value,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            
            var createdUserMaster = await userMasterRepository.CreateAsync(newUserMaster);
            userMasterId = createdUserMaster.Id;
        }
        else
        {
            userMasterId = userMaster.Id;
        }

        // Check if device exists
        var existingDevice = await deviceRepository.GetByDisplayDeviceIdAsync(request.DeviceId);
        
        if (existingDevice != null)
        {
            // Check if device is already assigned to another user
            if (!string.IsNullOrEmpty(existingDevice.UserId) && existingDevice.UserId != userMasterId)
            {
                throw new InvalidOperationException($"Device {request.DeviceId} is already assigned to another user");
            }

            // Assign device to current user master and set approval status to Pending
            existingDevice.UserId = userMasterId;
            existingDevice.ApprovalStatus = "Pending";
            //existingDevice.UpdatedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
            existingDevice.UpdatedAt = DateTime.UtcNow;


            var updatedDevice = await deviceRepository.UpdateAsync(existingDevice);

            return new UserDeviceAssignmentResponseDto
            {
                Id = updatedDevice.Id,
                DeviceId = updatedDevice.DisplayDeviceId ?? string.Empty,
                DeviceName = updatedDevice.DeviceName ?? string.Empty,
                UserId = updatedDevice.UserId ?? string.Empty,
                ApprovalStatus = updatedDevice.ApprovalStatus ?? "Pending",
                AssignedAt = DateTime.UtcNow
            };
        }
        else
        {
            // Create new device if it doesn't exist
            var newDevice = new Device
            {
                DisplayDeviceId = request.DeviceId,
                DeviceName = $"Device {request.DeviceId}",
                UserId = userMasterId,
                ApprovalStatus = "Pending",
                Status = "active",
                CreatedAt = DateTime.UtcNow,
                //UpdatedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                UpdatedAt = DateTime.UtcNow
            };

            var createdDevice = await deviceRepository.CreateAsync(newDevice);

            return new UserDeviceAssignmentResponseDto
            {
                Id = createdDevice.Id,
                DeviceId = createdDevice.DisplayDeviceId ?? string.Empty,
                DeviceName = createdDevice.DeviceName ?? string.Empty,
                UserId = createdDevice.UserId ?? string.Empty,
                ApprovalStatus = createdDevice.ApprovalStatus ?? "Pending",
                AssignedAt = DateTime.UtcNow
            };
        }
    }
} 