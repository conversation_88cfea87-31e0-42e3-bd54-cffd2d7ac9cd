using LipsBackend.Repository.Interfaces;
using LipsBackend.Service.DTOs;
using LipsBackend.Service.Interfaces;

namespace LipsBackend.Service.Services;

public class WorkApprovalService(IDeviceRepository deviceRepository) : IWorkApprovalService
{
    public async Task<WorkApprovalResponseDto?> ApproveWorkAsync(WorkApprovalRequestDto request)
    {
        var device = await deviceRepository.GetByDisplayDeviceIdAsync(request.DeviceId);
        if (device == null)
            return null;

        // Update approval status to Approved
        device.ApprovalStatus = "Approved";
        //device.UpdatedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        device.UpdatedAt = DateTime.UtcNow;

        await deviceRepository.UpdateAsync(device);

        return new WorkApprovalResponseDto
        {
            Message = "Congratulations your work approved",
            Success = true,
            DeviceId = device.Id,
            ApprovalStatus = device.ApprovalStatus,
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<WorkApprovalResponseDto?> RejectWorkAsync(WorkApprovalRequestDto request)
    {
        var device = await deviceRepository.GetByDisplayDeviceIdAsync(request.DeviceId);
        if (device == null)
            return null;

        // Update approval status to Rejected
        device.ApprovalStatus = "Rejected";
        //device.UpdatedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss");
        device.UpdatedAt = DateTime.UtcNow;

        await deviceRepository.UpdateAsync(device);

        return new WorkApprovalResponseDto
        {
            Message = "Sorry Your work rejected",
            Success = true,
            DeviceId = device.Id,
            ApprovalStatus = device.ApprovalStatus,
            UpdatedAt = DateTime.UtcNow
        };
    }
} 