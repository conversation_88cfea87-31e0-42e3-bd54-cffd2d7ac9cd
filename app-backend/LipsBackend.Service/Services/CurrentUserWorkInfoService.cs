using LipsBackend.Repository.Interfaces;
using LipsBackend.Service.DTOs;
using LipsBackend.Service.Interfaces;

namespace LipsBackend.Service.Services;

public class CurrentUserWorkInfoService(
    ICurrentUserProvider currentUserProvider,
    ILipsUserMasterRepository lipsUserMasterRepository,
    IWorkInfoRepository workInfoRepository,
    IDeviceRepository deviceRepository)
    : ICurrentUserWorkInfoService
{
    public async Task<CurrentUserWorkInfoDto?> GetCurrentUserWorkInfoAsync()
    {
        var currentUserId = currentUserProvider.GetCurrentUserId();
        if (currentUserId == null)
            return null;

        var lipsUserMaster = await lipsUserMasterRepository.GetByUserIdAsync(currentUserId.Value);
        if (lipsUserMaster == null)
            return null;
        
        var workInfos = await workInfoRepository.GetByUserIdAsync(lipsUserMaster.Id);
        var workInfo = workInfos.FirstOrDefault();
        if (workInfo == null)
            return null;

        var devices = await deviceRepository.GetByUserIdAsync(lipsUserMaster.Id);
        var device = devices.FirstOrDefault();
        if (device == null)
            return null;

        return new CurrentUserWorkInfoDto
        {
            work_name = workInfo.WorkName ?? string.Empty,
            display_device_id = device.DisplayDeviceId ?? string.Empty,
            status = device.Status,
            battery = $"{device.Battery ?? 0}%"
        };
    }
}