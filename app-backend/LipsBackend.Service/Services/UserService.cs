using LipsBackend.Domain.Helpers;
using LipsBackend.Repository.Interfaces;
using LipsBackend.Service.DTOs.Auth;
using LipsBackend.Service.Interfaces;
using LipsBackend.Service.Configuration;
using Microsoft.Extensions.Options;

namespace LipsBackend.Service.Services;

public class UserService(IUserRepository userRepository, IEmailService emailService, IOptions<EmailSettings> emailSettings) : IUserService
{
    private readonly EmailSettings _emailSettings = emailSettings.Value;

    public async Task<ForgotPasswordResponseDto?> ForgotPasswordAsync(ForgotPasswordRequestDto request)
    {
        var user = await userRepository.GetByEmailAsync(request.Email);
        if (user == null)
            return null;

        var otp = AppHelper.GenerateOtp();

        // Set OTP and expiration (10 minutes from now - matching Python version)
        var otpExpiredAt = DateTime.UtcNow.AddMinutes(10);
        user.Otp = otp;
        user.OtpExpiredAt = otpExpiredAt;

        await userRepository.UpdateAsync(user);

        // Development mode: Return OTP in response, no email sent
        if (_emailSettings.UseConsoleForDevelopment)
        {
            return new ForgotPasswordResponseDto
            {
                Otp = otp, // Return OTP for development
                OtpExpiredAt = otpExpiredAt,
                Message = "OTP generated successfully (Development Mode)"
            };
        }

        // Production mode: Send email, no OTP in response
        var emailSent = await emailService.SendOtpEmailAsync(user.Email, otp);

        return new ForgotPasswordResponseDto
        {
            Otp = string.Empty, // No OTP returned in production for security
            OtpExpiredAt = otpExpiredAt,
            Message = emailSent ? "OTP sent to your email successfully" : "Failed to send OTP email"
        };
    }

    public async Task<bool> ResetPasswordAsync(ResetPasswordRequestDto request)
    {
        var user = await userRepository.GetByEmailAsync(request.Email);
        if (user == null)
            return false;

        if (user.Otp != request.Otp)
            return false;

        if (user.OtpExpiredAt == null || user.OtpExpiredAt < DateTime.UtcNow)
            return false;

        user.Password = AppHelper.HashPasswordDjangoStyle(request.Password);
        
        // Clear OTP
        user.Otp = null;
        user.OtpExpiredAt = null;

        await userRepository.UpdateAsync(user);
        return true;
    }

    public async Task<bool> ChangePasswordAsync(long userId, ChangePasswordRequestDto request)
    {
        var user = await userRepository.GetByIdAsync(userId);
        if (user == null)
            return false;

        // Verify current password
        var isCurrentPasswordValid = AppHelper.VerifyDjangoPassword(request.CurrentPassword, user.Password);
        if (!isCurrentPasswordValid)
            return false;

        // Update to new password
        user.Password = AppHelper.HashPasswordDjangoStyle(request.NewPassword);

        await userRepository.UpdateAsync(user);
        return true;
    }
} 