using System.ComponentModel.DataAnnotations;

namespace LipsBackend.Service.DTOs.Auth;

public class ResetPasswordRequestDto
{
    [Required] public string Otp { get; set; } = string.Empty;

    [Required] [EmailAddress] public string Email { get; set; } = string.Empty;

    [Required] [MinLength(8)] public string Password { get; set; } = string.Empty;

    [Required] [Compare("Password")] public string ConfirmPassword { get; set; } = string.Empty;
}