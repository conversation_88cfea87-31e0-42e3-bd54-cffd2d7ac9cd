namespace LipsBackend.Service.DTOs;

public class DeviceDto
{
    public long Id { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string DisplayDeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public long? WorkId { get; set; }
    public DateTime? WorkTime { get; set; }
    public long Battery { get; set; }
    public string PreviousAlertInstruction { get; set; } = "0";
    public long SignalPeriod { get; set; } = 1000;
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}