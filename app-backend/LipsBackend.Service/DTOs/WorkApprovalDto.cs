namespace LipsBackend.Service.DTOs;

public class WorkApprovalRequestDto
{
    public string DeviceId { get; set; }
    // public string Status { get; set; } = string.Empty; // "approved" or "rejected"
}

public class WorkApprovalResponseDto
{
    public string Message { get; set; } = string.Empty;
    public bool Success { get; set; }
    public long DeviceId { get; set; }
    public string ApprovalStatus { get; set; } = string.Empty;
    public DateTime UpdatedAt { get; set; }
} 