namespace LipsBackend.Service.DTOs;

public class UserDeviceAssignmentRequestDto
{
    public string DeviceId { get; set; } = string.Empty;
}

public class UserDeviceAssignmentResponseDto
{
    public long Id { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public string ApprovalStatus { get; set; } = string.Empty;
    public DateTime AssignedAt { get; set; }
} 