﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LipsBackend.API", "LipsBackend.API\LipsBackend.API.csproj", "{CEB13268-879F-4917-B3E9-02471ADFC778}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LipsBackend.Service", "LipsBackend.Service\LipsBackend.Service.csproj", "{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LipsBackend.Domain", "LipsBackend.Domain\LipsBackend.Domain.csproj", "{FF201660-12EC-42A6-9636-9D88A67910DB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LipsBackend.Repository", "LipsBackend.Repository\LipsBackend.Repository.csproj", "{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LipsBackend.Tests", "LipsBackend.Tests\LipsBackend.Tests.csproj", "{094432BA-57EB-40B2-AC53-FB7A751D144D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|x64.ActiveCfg = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|x64.Build.0 = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|x86.ActiveCfg = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Debug|x86.Build.0 = Debug|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|Any CPU.Build.0 = Release|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|x64.ActiveCfg = Release|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|x64.Build.0 = Release|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|x86.ActiveCfg = Release|Any CPU
		{CEB13268-879F-4917-B3E9-02471ADFC778}.Release|x86.Build.0 = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|x64.Build.0 = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Debug|x86.Build.0 = Debug|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|x64.ActiveCfg = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|x64.Build.0 = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|x86.ActiveCfg = Release|Any CPU
		{B22CAA4C-CC9E-4678-BCCB-F23491DABB2C}.Release|x86.Build.0 = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|x64.Build.0 = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Debug|x86.Build.0 = Debug|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|x64.ActiveCfg = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|x64.Build.0 = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|x86.ActiveCfg = Release|Any CPU
		{FF201660-12EC-42A6-9636-9D88A67910DB}.Release|x86.Build.0 = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|x64.ActiveCfg = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|x64.Build.0 = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|x86.ActiveCfg = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Debug|x86.Build.0 = Debug|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|Any CPU.Build.0 = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|x64.ActiveCfg = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|x64.Build.0 = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|x86.ActiveCfg = Release|Any CPU
		{2CC5DD7C-1EF2-4662-8723-1A459E2A9B37}.Release|x86.Build.0 = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|x64.Build.0 = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Debug|x86.Build.0 = Debug|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|Any CPU.Build.0 = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|x64.ActiveCfg = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|x64.Build.0 = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|x86.ActiveCfg = Release|Any CPU
		{094432BA-57EB-40B2-AC53-FB7A751D144D}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {F9D4B22D-1F31-4852-990C-947E1D4B26C0}
	EndGlobalSection
EndGlobal
