﻿using System.Security.Cryptography;
using System.Text;

namespace LipsBackend.Domain.Helpers;

public class AppHelper
{
    public static bool VerifyDjangoPassword(string password, string djangoHash)
    {
        var parts = djangoHash.Split('$');
        if (parts.Length != 4 || parts[0] != "pbkdf2_sha256")
            return false;

        var iterations = int.Parse(parts[1]);
        var salt = parts[2];
        var storedHash = parts[3];

        using var pbkdf2 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(salt), iterations, HashAlgorithmName.SHA256);
        var derivedBytes = pbkdf2.GetBytes(32);
        var computedHash = Convert.ToBase64String(derivedBytes);

        return storedHash == computedHash;
    }

    public static string HashPasswordDjangoStyle(string password)
    {
        var iterations = 600000;
        var saltBytes = new byte[16];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(saltBytes);
        var salt = Convert.ToBase64String(saltBytes)[..22].Replace('+', '.'); // Truncate and clean salt for DB

        using var pbkdf2 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(salt), iterations, HashAlgorithmName.SHA256);
        var hash = Convert.ToBase64String(pbkdf2.GetBytes(32));

        return $"pbkdf2_sha256${iterations}${salt}${hash}";
    }

    public static string GenerateOtp()
    {
        var generator = new Random();
        var random = generator.Next( 100000, 999999).ToString("D6");
        return random;
    }
}