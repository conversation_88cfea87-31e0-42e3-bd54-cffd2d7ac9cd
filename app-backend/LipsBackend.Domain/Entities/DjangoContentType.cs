﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("django_content_type")]
public class DjangoContentType
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("app_label")]
    public string AppLabel { get; set; } = string.Empty;

    [Column("model")]
    public string Model { get; set; } = string.Empty;
}