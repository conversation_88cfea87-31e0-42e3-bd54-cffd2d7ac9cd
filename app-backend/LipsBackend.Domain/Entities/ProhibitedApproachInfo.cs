﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_prohibited_approach_info")]
public class ProhibitedApproachInfo
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("latitude")]
    public double? Latitude { get; set; }

    [Column("longitude")]
    public double? Longitude { get; set; }

    [Column("x_vector")]
    public string? XVector { get; set; }

    [Column("y_vector")]
    public string? YVector { get; set; }

    [Column("base_area")]
    public string? BaseArea { get; set; }

    [Column("extraction_area")]
    public string? ExtractionArea { get; set; }

    [Column("map_code")]
    public string? MapCode { get; set; }

    [Column("prefectures")]
    public string? Prefectures { get; set; }

    [Column("municipalities")]
    public string? Municipalities { get; set; }

    [Column("created_at")]
    public string? CreatedAt { get; set; }

    [Column("updated_at")]
    public string? UpdatedAt { get; set; }
}