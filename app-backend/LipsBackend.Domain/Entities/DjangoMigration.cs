﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("django_migrations")]
public class DjangoMigration
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("app")]
    public string App { get; set; } = string.Empty;

    [Column("name")]
    public string Name { get; set; } = string.Empty;

    [Column("applied")]
    public DateTime Applied { get; set; }
}