﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_permited_approach_info")]
public class PermitedApproachInfo
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("area_element_num")]
    public long? AreaElementNum { get; set; }

    [Column("area_info")]
    public string? AreaInfo { get; set; }

    [Column("created_at")]
    public string? CreatedAt { get; set; }

    [Column("updated_at")]
    public string? UpdatedAt { get; set; }

    [Column("work_id")]
    public string? WorkId { get; set; }
}