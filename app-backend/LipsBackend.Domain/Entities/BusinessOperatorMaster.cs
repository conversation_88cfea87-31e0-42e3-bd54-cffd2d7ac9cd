using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_business_operator_masters")]
public class BusinessOperatorMaster
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("domain_name")]
    public string DomainName { get; set; } = string.Empty;

    [Column("business_operator_name")]
    public string BusinessOperatorName { get; set; } = string.Empty;
} 