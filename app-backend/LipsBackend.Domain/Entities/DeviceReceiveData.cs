﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_device_receive_data")]
public class DeviceReceiveData
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("time")]
    public string? Time { get; set; }

    [Column("previous_alert_id")]
    public string? PreviousAlertId { get; set; }

    [Column("battery")]
    public long? Battery { get; set; }

    [Column("dop")]
    public long? Dop { get; set; }

    [Column("ns_latitude_identifier")]
    public string? NsLatitudeIdentifier { get; set; }

    [Column("latitude")]
    public long? Latitude { get; set; }

    [Column("ew_longitude_identifier")]
    public string? EwLongitudeIdentifier { get; set; }

    [Column("longitude")]
    public long? Longitude { get; set; }

    [Column("x_acceleration")]
    public long? XAcceleration { get; set; }

    [Column("y_acceleration")]
    public long? YAcceleration { get; set; }

    [Column("z_acceleration")]
    public long? ZAcceleration { get; set; }

    [Column("alive_count")]
    public long? AliveCount { get; set; }

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [Column("device_id")]
    public string? DeviceId { get; set; }
}