﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_historical_judge_alert_instruction")]
public class HistoricalJudgeAlertInstruction
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("ns_latitude_identifier")]
    public string? NsLatitudeIdentifier { get; set; }

    [Column("latitude")]
    public long? Latitude { get; set; }

    [Column("ew_longitude_identifier")]
    public string? EwLongitudeIdentifier { get; set; }

    [Column("longitude")]
    public long? Longitude { get; set; }

    [Column("utm_x")]
    public string? UtmX { get; set; }

    [Column("utm_y")]
    public string? UtmY { get; set; }

    [Column("x_acceleration")]
    public long? XAcceleration { get; set; }

    [Column("y_acceleration")]
    public long? YAcceleration { get; set; }

    [Column("z_acceleration")]
    public long? ZAcceleration { get; set; }

    [Column("alert_instruction")]
    public long? AlertInstruction { get; set; }

    [Column("alert_id")]
    public string? AlertId { get; set; }

    [Column("previous_alert_id")]
    public string? PreviousAlertId { get; set; }

    [Column("work_time")]
    public string? WorkTime { get; set; }

    [Column("group_num")]
    public string? GroupNum { get; set; }

    [Column("alive_count")]
    public long? AliveCount { get; set; }

    [Column("created_at")]
    public string? CreatedAt { get; set; }

    [Column("updated_at")]
    public string? UpdatedAt { get; set; }

    [Column("device_id")]
    public string? DeviceId { get; set; }
}