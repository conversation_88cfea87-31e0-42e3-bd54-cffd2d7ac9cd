using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_work_info")]
public class WorkInfo
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("work_name")]
    public string? WorkName { get; set; }

    [Column("group_num")]
    public string? GroupNum { get; set; }

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [Column("user_id")]
    public string? UserId { get; set; }
}