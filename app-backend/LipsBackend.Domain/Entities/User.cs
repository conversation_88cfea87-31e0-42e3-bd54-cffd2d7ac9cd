using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("usermanagement_user")]
public class User
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("username")]
    public string Username { get; set; } = string.Empty;

    [Column("email")]
    public string Email { get; set; } = string.Empty;

    [Column("password")]
    public string Password { get; set; } = string.Empty;

    [Column("full_name")]
    public string? FullName { get; set; }

    [Column("status")]
    public string Status { get; set; } = string.Empty;

    [Column("phone_number")]
    public string? PhoneNumber { get; set; }

    [Column("otp")]
    public string? Otp { get; set; }

    [Column("otp_expired_at")]
    public DateTime? OtpExpiredAt { get; set; }

    [Column("role_id")]
    public long? RoleId { get; set; }

    [Column("company_name")]
    public string CompanyName { get; set; } = string.Empty;

    // Navigation property
    [ForeignKey("RoleId")]
    public virtual Role? Role { get; set; }
}