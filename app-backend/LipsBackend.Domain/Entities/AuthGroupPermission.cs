﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("auth_group_permissions")]
public class AuthGroupPermission
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("group_id")]
    public int GroupId { get; set; }

    [Column("permission_id")]
    public int PermissionId { get; set; }
}