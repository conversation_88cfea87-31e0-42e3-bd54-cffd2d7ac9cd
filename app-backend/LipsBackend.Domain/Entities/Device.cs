using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_device_masters")]
public class Device
{
    [Key]
    [Column("id")]
    public long Id { get; set; }

    [Column("display_device_id")]
    public string? DisplayDeviceId { get; set; }

    [Column("device_name")]
    public string? DeviceName { get; set; }

    [Column("work_time")]
    public string? WorkTime { get; set; }

    [Column("battery")]
    public long? Battery { get; set; }

    [Column("previous_alert_instruction")]
    public string? PreviousAlertInstruction { get; set; }

    [Column("signal_period")]
    public long? SignalPeriod { get; set; }

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    [Column("user_id")]
    public string? UserId { get; set; }

    [Column("work_id")]
    public long? WorkId { get; set; }

    [Column("business_operator_id")]
    public int? BusinessOperatorId { get; set; }

    [Column("status")]
    public string? Status { get; set; }

    [Column("approval_status")]
    public string? ApprovalStatus { get; set; }
}