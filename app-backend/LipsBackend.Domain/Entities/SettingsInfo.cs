﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_settings_info")]
public class SettingsInfo
{
    [Key]
    [Column("user_id")]
    public string UserId { get; set; } = string.Empty;

    [Column("key_name")]
    public string? KeyName { get; set; }

    [Column("value")]
    public string? Value { get; set; }

    [Column("summary")]
    public string? Summary { get; set; }

    [Column("created_at")]
    public string? CreatedAt { get; set; }

    [Column("updated_at")]
    public string? UpdatedAt { get; set; }
}