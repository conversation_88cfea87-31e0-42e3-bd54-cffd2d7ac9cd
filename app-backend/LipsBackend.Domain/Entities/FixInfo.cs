﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("lips_fix_info")]
public class FixInfo
{
    [Key]
    [Column("id")]
    public string Id { get; set; } = string.Empty;

    [Column("key_name")]
    public string? KeyName { get; set; }

    [Column("val")]
    public string? Val { get; set; }

    [Column("summary")]
    public string? Summary { get; set; }

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}