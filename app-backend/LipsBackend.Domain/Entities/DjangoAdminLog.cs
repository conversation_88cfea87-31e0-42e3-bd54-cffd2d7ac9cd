﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("django_admin_log")]
public class DjangoAdminLog
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("action_time")]
    public DateTime ActionTime { get; set; }

    [Column("object_id")]
    public string? ObjectId { get; set; }

    [Column("object_repr")]
    public string ObjectRepr { get; set; } = string.Empty;

    [Column("action_flag")]
    public ushort ActionFlag { get; set; }

    [Column("change_message")]
    public string ChangeMessage { get; set; } = string.Empty;

    [Column("content_type_id")]
    public int? ContentTypeId { get; set; }

    [Column("user_id")]
    public long UserId { get; set; }
}