﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("django_session")]
public class DjangoSession
{
    [Key]
    [Column("session_key")]
    public string SessionKey { get; set; } = string.Empty;

    [Column("session_data")]
    public string SessionData { get; set; } = string.Empty;

    [Column("expire_date")]
    public DateTime ExpireDate { get; set; }
}