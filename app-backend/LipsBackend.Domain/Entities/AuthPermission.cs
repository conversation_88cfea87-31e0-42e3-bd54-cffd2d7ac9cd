﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LipsBackend.Domain.Entities;

[Table("auth_permission")]
public class AuthPermission
{
    [Key]
    [Column("id")]
    public int Id { get; set; }

    [Column("name")]
    public string Name { get; set; } = string.Empty;

    [Column("content_type_id")]
    public int ContentTypeId { get; set; }

    [Column("codename")]
    public string Codename { get; set; } = string.Empty;
}