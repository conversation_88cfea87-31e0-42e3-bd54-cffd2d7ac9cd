# Lips Backend API

A .NET 9 Web API solution implementing user management and device services using Onion Architecture.

## Architecture

This solution follows the Onion Architecture pattern with the following layers:

- **LipsBackend.API**: Web API controllers, Swagger, middleware
- **LipsBackend.Service**: Business logic, DTOs, Services, Interfaces
- **LipsBackend.Domain**: Domain Entities and Helpers
- **LipsBackend.Repository**: Database access, Repositories, AppDbContext
- **LipsBackend.Tests**: Unit & Integration Tests

## Tech Stack

- .NET 9 Web API
- Entity Framework Core with MySQL
- AutoMapper for object mapping
- JWT Authentication
- Django-style password hashing (PBKDF2 with SHA256)
- SMTP email service
- Swagger for API documentation
- Onion Architecture pattern
- Repository & Service patterns

## API Endpoints

### Authentication
- `POST /api/auth/login` - Authenticate users and get JWT token
- `POST /api/auth/change-password` - Update user password (requires authentication)

### User Management
- `POST /api/v1/userapi/forgot-password` - Request password reset OTP via email
- `POST /api/v1/userapi/reset-password` - Reset password using OTP

### Work Information
- `GET /api/v1/lips-work-info/work/` - Get work information for authenticated user

### Business Operator Devices
- `GET /api/v1/business-operator-devices/` - Get business operator devices for authenticated user

## Getting Started

### Prerequisites
- .NET 9 SDK
- MySQL Server

### Database Setup
1. Update the connection string in `appsettings.json`:
   ```json
   "ConnectionStrings": {
     "DefaultConnection": "Server=localhost;Port=3306;Database=lps-db-2;User=root;Password=password;"
   }
   ```

2. Run Entity Framework migrations:
   ```bash
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

### Running the API
```bash
dotnet run --project LipsBackend.API
```

The API will be available at:
- **HTTPS**: `https://localhost:7185`
- **HTTP**: `http://localhost:5273`
- **Swagger**: `https://localhost:7185/swagger`

## Email Configuration

### Development Mode (Default)
The application runs in development mode by default with console email output:

```json
{
  "Email": {
    "UseConsoleForDevelopment": true
  }
}
```

**Behavior**:
- ✅ OTP returned in forgot-password API response
- ✅ Email content logged to console
- ✅ No actual emails sent

### Production Setup

**⚠️ IMPORTANT: Configure SMTP before production deployment**

1. **Update appsettings.json**:
   ```json
   {
     "Email": {
       "SmtpHost": "smtp.gmail.com",
       "SmtpPort": 587,
       "UseTls": true,
       "FromEmail": "<EMAIL>",
       "FromName": "Your App Name",
       "Username": "your-smtp-username",
       "Password": "your-smtp-password",
       "UseConsoleForDevelopment": false
     }
   }
   ```

2. **Gmail Setup** (Recommended):
   - Enable 2-Factor Authentication
   - Generate App Password: [Google App Passwords](https://myaccount.google.com/apppasswords)
   - Use App Password (not regular password) in configuration

3. **Alternative SMTP Providers**:
   - **SendGrid**: `smtp.sendgrid.net:587`
   - **AWS SES**: `email-smtp.region.amazonaws.com:587`
   - **Mailgun**: `smtp.mailgun.org:587`

**Production Behavior**:
- ✅ Real emails sent via SMTP
- ✅ OTP NOT returned in API response (security)
- ✅ Email delivery to user's inbox

## Security

### Authentication
- JWT Bearer token authentication
- Token expiration configurable via `Jwt:ExpiryInDays`

### Password Security  
- Django-style PBKDF2 password hashing with SHA256
- 600,000 iterations for enhanced security
- Salt-based hashing prevents rainbow table attacks

### Email Security
- SMTP credentials validation
- TLS encryption for email transmission
- OTP expiration (10 minutes)
- Production mode hides OTP from API responses

## Configuration

### Required Environment Variables (Production)
```bash
# Recommended: Use environment variables for sensitive data
EMAIL_USERNAME=your-smtp-username
EMAIL_PASSWORD=your-smtp-password
```

### Key Settings
- `UseConsoleForDevelopment: false` - **Required for production**
- `Jwt:Key` - **Change default secret key**
- `ConnectionStrings:DefaultConnection` - **Update for your database**

## Development

### Testing Email Functionality
```bash
# Development mode - OTP returned in response
curl -X POST "https://localhost:7185/api/v1/userapi/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Response includes OTP for testing:
{
  "Otp": "123456",
  "Message": "OTP generated successfully (Development Mode)"
}
```

### Building and Testing
```bash
# Build the solution
dotnet build

# Run tests
dotnet test

# Run with watch mode
dotnet watch run --project LipsBackend.API
```

## Deployment Checklist

Before deploying to production:

- [ ] Set `UseConsoleForDevelopment: false`
- [ ] Configure SMTP credentials (Username/Password)  
- [ ] Update `FromEmail` to your domain
- [ ] Change default JWT secret key
- [ ] Test email sending with real email address
- [ ] Update database connection string
- [ ] Configure environment variables for secrets

## Error Handling

The application includes comprehensive error handling:
- Invalid credentials return appropriate error messages
- Missing SMTP configuration prevents startup in production
- JWT token validation with proper error responses
- Database connection error handling 